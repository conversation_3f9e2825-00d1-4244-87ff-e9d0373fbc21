Metadata-Version: 2.4
Name: prism-hourly-dataset
Version: 1.0.0
Summary: Create hourly precipitation datasets by combining daily PRISM data with NCEP Stage IV radar data
Author-email: PRISM Hourly Dataset Contributors <<EMAIL>>
Maintainer-email: PRISM Hourly Dataset Contributors <<EMAIL>>
License-Expression: MIT
Project-URL: Homepage, https://github.com/your-org/prism-hourly-dataset
Project-URL: Documentation, https://prism-hourly-dataset.readthedocs.io/
Project-URL: Repository, https://github.com/your-org/prism-hourly-dataset.git
Project-URL: Bug Tracker, https://github.com/your-org/prism-hourly-dataset/issues
Project-URL: Changelog, https://github.com/your-org/prism-hourly-dataset/blob/main/CHANGELOG.md
Keywords: precipitation,climate,weather,PRISM,NCEP,radar,temporal-disaggregation,geospatial,netcdf
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Science/Research
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Scientific/Engineering :: Atmospheric Science
Classifier: Topic :: Scientific/Engineering :: GIS
Classifier: Topic :: Scientific/Engineering :: Hydrology
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: click>=8.0.0
Requires-Dist: numpy>=1.20.0
Requires-Dist: pandas>=1.3.0
Requires-Dist: xarray>=0.19.0
Requires-Dist: netcdf4>=1.5.0
Requires-Dist: rasterio>=1.2.0
Requires-Dist: requests>=2.25.0
Requires-Dist: pyyaml>=5.4.0
Requires-Dist: scipy>=1.7.0
Requires-Dist: tqdm>=4.60.0
Requires-Dist: pyproj>=3.0.0
Provides-Extra: dev
Requires-Dist: pytest>=6.0.0; extra == "dev"
Requires-Dist: pytest-cov>=2.12.0; extra == "dev"
Requires-Dist: black>=21.0.0; extra == "dev"
Requires-Dist: isort>=5.9.0; extra == "dev"
Requires-Dist: flake8>=3.9.0; extra == "dev"
Requires-Dist: mypy>=0.910; extra == "dev"
Requires-Dist: pre-commit>=2.15.0; extra == "dev"
Requires-Dist: sphinx>=4.0.0; extra == "dev"
Requires-Dist: sphinx-rtd-theme>=0.5.0; extra == "dev"
Provides-Extra: test
Requires-Dist: pytest>=6.0.0; extra == "test"
Requires-Dist: pytest-cov>=2.12.0; extra == "test"
Requires-Dist: pytest-mock>=3.6.0; extra == "test"
Provides-Extra: docs
Requires-Dist: sphinx>=4.0.0; extra == "docs"
Requires-Dist: sphinx-rtd-theme>=0.5.0; extra == "docs"
Requires-Dist: sphinx-click>=3.0.0; extra == "docs"
Requires-Dist: myst-parser>=0.15.0; extra == "docs"
Dynamic: license-file

# PRISM Hourly Dataset Creator

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

A Python package for creating high-quality hourly precipitation datasets by combining daily PRISM data with NCEP Stage IV radar data through temporal disaggregation.

## Overview

The PRISM Hourly Dataset Creator addresses the need for high-resolution temporal precipitation data by leveraging the spatial accuracy of PRISM (Parameter-elevation Regressions on Independent Slopes Model) daily precipitation data and the temporal resolution of NCEP Stage IV radar precipitation estimates.

### Key Features

- **Automated Data Download**: Download PRISM daily and NCEP Stage IV radar data
- **Spatial Processing**: Reproject and align data to common grids
- **Temporal Disaggregation**: Create hourly estimates using radar temporal patterns
- **Quality Control**: Comprehensive validation and quality checks
- **Command-Line Interface**: Easy-to-use CLI for all operations
- **Configurable**: Flexible configuration system with YAML support

### Scientific Background

This package implements a temporal disaggregation methodology that:

1. **Preserves PRISM Accuracy**: Maintains the high spatial accuracy and bias-correction of PRISM daily precipitation totals
2. **Adds Temporal Detail**: Uses NCEP Stage IV radar data to provide realistic hourly precipitation patterns
3. **Ensures Mass Conservation**: Hourly estimates sum exactly to the original PRISM daily totals
4. **Handles Missing Data**: Robust handling of data gaps and quality issues

## Installation

### Prerequisites

- Python 3.8 or higher
- GDAL/OGR libraries
- NetCDF libraries

### System Dependencies

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install gdal-bin libgdal-dev libnetcdf-dev
```

**CentOS/RHEL:**
```bash
sudo yum install gdal gdal-devel netcdf netcdf-devel
```

**macOS (with Homebrew):**
```bash
brew install gdal netcdf
```

**Windows:**
- Install GDAL from [OSGeo4W](https://trac.osgeo.org/osgeo4w/)
- Or use conda: `conda install gdal netcdf4`

### Package Installation

#### From Source (Recommended)

```bash
# Clone the repository
git clone https://github.com/your-org/prism-hourly-dataset.git
cd prism-hourly-dataset

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install package
pip install -e .
```

#### Using pip (when available)

```bash
pip install prism-hourly-dataset
```

## Quick Start

### 1. Basic Configuration

Create a configuration file or use the default settings:

```bash
# View current configuration
prism-hourly info

# Create a custom configuration file
cp config/config.yaml my_config.yaml
# Edit my_config.yaml as needed
```

### 2. Download Data

Download PRISM daily precipitation data:

```bash
# Download PRISM data for 2023
prism-hourly download-prism --start-date 2023-01-01 --end-date 2023-12-31
```

Download NCEP Stage IV radar data:

```bash
# Download NCEP data for 2023
prism-hourly download-ncep --start-year 2023 --end-year 2023
```

### 3. Process Data

Process PRISM data to NetCDF format:

```bash
prism-hourly process-prism --prism-dir ./data/prism_raw --output-dir ./data/prism_processed
```

### 4. Create Hourly Datasets

Generate hourly precipitation estimates:

```bash
prism-hourly create-hourly \
    --prism-dir ./data/prism_processed \
    --ncep-dir ./data/ncep_processed \
    --output-dir ./data/prism_hourly
```

### 5. Validate Results

Validate the created datasets:

```bash
prism-hourly validate --input-dir ./data/prism_hourly --check-temporal --check-spatial
```

## Detailed Usage

### Configuration

The package uses a hierarchical configuration system:

1. **Default configuration** (built-in)
2. **Configuration file** (`config/config.yaml` or custom file)
3. **Environment variables** (override specific settings)
4. **Command-line options** (highest priority)

#### Configuration File Example

```yaml
# config/config.yaml
download:
  prism_base_url: "https://data.prism.oregonstate.edu/daily/ppt"
  prism_output_dir: "./data/prism_raw"
  ncep_base_url: "https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4"
  ncep_output_dir: "./data/ncep_raw"
  download_delay: 2.0

processing:
  target_grid_file: "./config/Terrain.tif"
  resolution: 4000.0  # meters
  resampling_method: "cubic"
  prism_processed_dir: "./data/prism_processed"
  ncep_processed_dir: "./data/ncep_processed"

disaggregation:
  hour_range_start: 12  # 12Z
  hour_range_end: 36    # 11Z next day
  output_dir: "./data/prism_hourly"
  quality_control: true

logging:
  level: "INFO"
  file_path: "./logs/prism_hourly.log"
```

#### Environment Variables

Override configuration with environment variables:

```bash
export PRISM_HOURLY_OUTPUT_DIR="/custom/output/path"
export PRISM_HOURLY_LOG_LEVEL="DEBUG"
export PRISM_HOURLY_RESOLUTION="5000"
```

### Advanced Usage

#### Python API

Use the package programmatically:

```python
from prism_hourly import PRISMDownloader, DataProcessor, TemporalDisaggregator
from prism_hourly.utils import ConfigManager
from datetime import date

# Load configuration
config_manager = ConfigManager()
config = config_manager.get_config()

# Download PRISM data
downloader = PRISMDownloader(output_dir="./data/prism_raw")
downloader.download_date_range(date(2023, 1, 1), date(2023, 1, 31))

# Process data
processor = DataProcessor(
    target_grid_file="./config/Terrain.tif",
    resolution=4000.0
)

# Create hourly datasets
disaggregator = TemporalDisaggregator(
    prism_dir="./data/prism_processed",
    ncep_dir="./data/ncep_processed",
    output_dir="./data/prism_hourly"
)

stats = disaggregator.process_all_prism_files()
print(f"Processed {stats['processed']} files")
```

#### Batch Processing

Process multiple years efficiently:

```bash
#!/bin/bash
# Process multiple years

for year in {2020..2023}; do
    echo "Processing year $year"

    # Download data
    prism-hourly download-prism \
        --start-date ${year}-01-01 \
        --end-date ${year}-12-31 \
        --output-dir ./data/prism_raw_${year}

    # Process data
    prism-hourly process-prism \
        --prism-dir ./data/prism_raw_${year} \
        --output-dir ./data/prism_processed_${year}

    # Create hourly datasets
    prism-hourly create-hourly \
        --prism-dir ./data/prism_processed_${year} \
        --output-dir ./data/prism_hourly_${year}
done
```

## Data Sources

### PRISM Data

- **Source**: [PRISM Climate Group, Oregon State University](https://prism.oregonstate.edu/)
- **Product**: Daily precipitation (4km resolution)
- **Format**: BIL (Band Interleaved by Line) in ZIP archives
- **Coverage**: Continental United States
- **Temporal Range**: 1981-present (with ~6 month delay)

### NCEP Stage IV Data

- **Source**: [UCAR Research Data Archive](https://rda.ucar.edu/)
- **Product**: Hourly precipitation analysis (4km resolution)
- **Format**: GRIB2 files in TAR archives
- **Coverage**: Continental United States
- **Temporal Range**: 2002-present

## Output Format

The package creates hourly NetCDF files with the following structure:

```
PRISM_YYYYMMDDHH.nc
├── dimensions:
│   ├── time: 1
│   ├── y: 896
│   └── x: 1152
├── variables:
│   ├── time(time): datetime64[ns]
│   ├── y(y): float64
│   ├── x(x): float64
│   └── precipitation(time, y, x): float32
└── attributes:
    ├── Projection: <CRS WKT>
    └── creation_date: <timestamp>
```

### Variable Attributes

- **precipitation**:
  - `units`: "mm"
  - `long_name`: "PRISM-adjusted hourly precipitation"
  - `description`: "Hourly precipitation derived from PRISM daily totals using NCEP Stage IV temporal patterns"
  - `missing_value`: -9999.0

## Quality Control

The package includes comprehensive quality control measures:

### Data Validation

- **File integrity checks**: Verify NetCDF file structure and readability
- **Value range validation**: Check for physically reasonable precipitation values
- **Spatial consistency**: Ensure consistent grid properties across files
- **Temporal consistency**: Validate time series continuity and detect gaps

### Quality Flags

- **Missing data handling**: Proper treatment of NaN values and missing files
- **Extreme value detection**: Identification of suspicious precipitation values
- **Mass conservation**: Verification that hourly totals match daily PRISM values

### Validation Reports

Generate comprehensive validation reports:

```bash
prism-hourly validate \
    --input-dir ./data/prism_hourly \
    --check-temporal \
    --check-spatial
```

## Performance and Scalability

### System Requirements

**Minimum Requirements:**
- RAM: 8 GB
- Storage: 100 GB free space (for 1 year of data)
- CPU: 4 cores

**Recommended for Production:**
- RAM: 32 GB or more
- Storage: 1 TB+ (SSD preferred)
- CPU: 8+ cores
- Network: High-bandwidth connection for data downloads

### Processing Performance

Typical processing times (on recommended hardware):

- **PRISM Download**: ~2 minutes per year
- **NCEP Download**: ~30 minutes per year
- **PRISM Processing**: ~5 minutes per year
- **Hourly Creation**: ~15 minutes per year

### Memory Usage

- **Peak memory usage**: ~4-8 GB per process
- **Concurrent processing**: Supports parallel processing of multiple files
- **Memory optimization**: Efficient chunked processing for large datasets

## Troubleshooting

### Common Issues

#### Download Failures

```bash
# Check network connectivity
curl -I https://data.prism.oregonstate.edu/

# Verify credentials for NCEP data
# (if authentication is required)

# Retry with increased delay
prism-hourly download-prism --start-date 2023-01-01 --end-date 2023-01-31 --delay 5
```

#### Processing Errors

```bash
# Check GDAL installation
gdalinfo --version

# Verify input files
prism-hourly validate --input-dir ./data/prism_raw

# Check disk space
df -h

# Enable debug logging
prism-hourly --log-level DEBUG process-prism
```

#### Memory Issues

```bash
# Monitor memory usage
top -p $(pgrep -f prism-hourly)

# Process smaller batches
prism-hourly create-hourly --batch-size 10

# Use swap space if needed
sudo swapon /swapfile
```

### Error Messages

| Error | Cause | Solution |
|-------|-------|----------|
| `GDAL not found` | Missing GDAL installation | Install GDAL system libraries |
| `NetCDF read error` | Corrupted file | Re-download the file |
| `Grid mismatch` | Inconsistent spatial grids | Check target grid file |
| `Memory error` | Insufficient RAM | Reduce batch size or add swap |
| `Permission denied` | File access issues | Check file permissions |

### Getting Help

1. **Check the logs**: Enable debug logging with `--log-level DEBUG`
2. **Validate inputs**: Use the `validate` command to check data integrity
3. **Review configuration**: Use `prism-hourly info` to verify settings
4. **Search issues**: Check the [GitHub Issues](https://github.com/your-org/prism-hourly-dataset/issues)
5. **Report bugs**: Create a new issue with detailed error information

## API Reference

### Core Classes

#### PRISMDownloader

Downloads PRISM daily precipitation data.

```python
from prism_hourly import PRISMDownloader

downloader = PRISMDownloader(
    output_dir="./data/prism_raw",
    delay_seconds=2.0
)

# Download date range
downloader.download_date_range(start_date, end_date)

# Verify downloads
verification = downloader.verify_downloads()
```

#### NCEPDownloader

Downloads NCEP Stage IV radar data.

```python
from prism_hourly import NCEPDownloader

downloader = NCEPDownloader(output_dir="./data/ncep_raw")

# Download specific months
downloader.download_monthly_files(["202301", "202302"])

# Download year range
downloader.download_year_range(2023, 2023)
```

#### DataProcessor

Processes and reprojects data to common grids.

```python
from prism_hourly import DataProcessor

processor = DataProcessor(
    target_grid_file="./config/Terrain.tif",
    resolution=4000.0,
    nodata_value=-9999.0
)

# Process PRISM file
output_file = processor.process_prism_file(prism_zip, output_dir)

# Process radar file
output_file = processor.process_radar_file(radar_file, output_dir)
```

#### TemporalDisaggregator

Creates hourly datasets using temporal disaggregation.

```python
from prism_hourly import TemporalDisaggregator

disaggregator = TemporalDisaggregator(
    prism_dir="./data/prism_processed",
    ncep_dir="./data/ncep_processed",
    output_dir="./data/prism_hourly",
    hour_range=(12, 36)
)

# Process all files
stats = disaggregator.process_all_prism_files()

# Process single file
output_files = disaggregator.process_prism_file(prism_file)
```

### Utility Classes

#### ConfigManager

Manages configuration settings.

```python
from prism_hourly.utils import ConfigManager

config_manager = ConfigManager("custom_config.yaml")
config = config_manager.get_config()

# Access configuration sections
download_config = config.download
processing_config = config.processing
```

#### ValidationUtils

Provides data validation functions.

```python
from prism_hourly.utils import ValidationUtils

# Validate NetCDF file
result = ValidationUtils.validate_netcdf_file("data.nc")

# Check temporal consistency
result = ValidationUtils.validate_temporal_consistency(file_list)

# Validate data ranges
result = ValidationUtils.validate_data_range("data.nc", min_value=0, max_value=500)
```

## Contributing

We welcome contributions to improve the PRISM Hourly Dataset Creator!

### Development Setup

```bash
# Clone the repository
git clone https://github.com/your-org/prism-hourly-dataset.git
cd prism-hourly-dataset

# Create development environment
python -m venv dev-env
source dev-env/bin/activate

# Install in development mode
pip install -e ".[dev]"

# Install pre-commit hooks
pre-commit install
```

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=prism_hourly --cov-report=html

# Run specific test categories
pytest tests/test_download.py
pytest tests/test_processing.py
```

### Code Style

We use the following tools for code quality:

- **Black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting
- **mypy**: Type checking

```bash
# Format code
black src/
isort src/

# Check linting
flake8 src/

# Type checking
mypy src/
```

### Submitting Changes

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/new-feature`
3. **Make your changes** with appropriate tests
4. **Run the test suite**: `pytest`
5. **Submit a pull request** with a clear description

### Reporting Issues

When reporting issues, please include:

- **Python version** and operating system
- **Package version** or commit hash
- **Complete error message** and stack trace
- **Minimal example** to reproduce the issue
- **Expected vs. actual behavior**

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Citation

If you use this package in your research, please cite:

```bibtex
@software{prism_hourly_dataset,
  title={PRISM Hourly Dataset Creator},
  author={Your Name and Contributors},
  year={2024},
  url={https://github.com/your-org/prism-hourly-dataset},
  version={1.0.0}
}
```

## Acknowledgments

- **PRISM Climate Group** at Oregon State University for providing high-quality daily precipitation data
- **NCEP/NWS** for the Stage IV radar precipitation analysis
- **UCAR Research Data Archive** for data distribution and access
- **Contributors** who have helped improve this package

## Related Projects

- [PRISM Climate Data](https://prism.oregonstate.edu/) - Source of daily precipitation data
- [NCEP Stage IV](https://www.emc.ncep.noaa.gov/mmb/ylin/pcpanl/stage4/) - Radar precipitation analysis
- [xarray](https://xarray.pydata.org/) - Multi-dimensional labeled data arrays
- [rasterio](https://rasterio.readthedocs.io/) - Geospatial raster I/O

## Changelog

### Version 1.0.0 (2024-01-XX)

- Initial release
- Complete temporal disaggregation pipeline
- Command-line interface
- Comprehensive validation and quality control
- Configuration management system
- Full documentation and examples

---

**For more information, visit the [project documentation](https://your-org.github.io/prism-hourly-dataset/) or [GitHub repository](https://github.com/your-org/prism-hourly-dataset).**
