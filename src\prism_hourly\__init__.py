"""
PRISM Hourly Dataset Creator

A Python package for creating high-quality hourly precipitation datasets by combining
daily PRISM data with NCEP Stage IV radar data through temporal disaggregation.

This package provides tools for:
- Downloading PRISM daily and NCEP Stage IV radar precipitation data
- Processing and reprojecting data to common grids
- Creating hourly precipitation estimates using temporal disaggregation
- Validating and quality-controlling the resulting datasets

Example:
    Basic usage of the package:

    >>> from prism_hourly import PRISMDownloader, TemporalDisaggregator
    >>> from datetime import date
    >>> 
    >>> # Download PRISM data
    >>> downloader = PRISMDownloader(output_dir="./data/prism_raw")
    >>> downloader.download_date_range(date(2023, 1, 1), date(2023, 1, 31))
    >>> 
    >>> # Create hourly datasets
    >>> disaggregator = TemporalDisaggregator(
    ...     prism_dir="./data/prism_processed",
    ...     ncep_dir="./data/ncep_processed",
    ...     output_dir="./data/prism_hourly"
    ... )
    >>> stats = disaggregator.process_all_prism_files()
"""

__version__ = "1.0.0"
__author__ = "PRISM Hourly Dataset Contributors"
__email__ = "<EMAIL>"
__license__ = "MIT"

# Import main classes for easy access
from .data_download.prism_downloader import PRISMDownloader
from .data_download.ncep_downloader import NCEPDownloader
from .data_processing.data_processor import DataProcessor
from .data_processing.temporal_disaggregator import TemporalDisaggregator
from .data_processing.grid_operations import GridOperations
from .utils.config_manager import ConfigManager
from .utils.validation_utils import ValidationUtils

# Define what gets imported with "from prism_hourly import *"
__all__ = [
    "PRISMDownloader",
    "NCEPDownloader", 
    "DataProcessor",
    "TemporalDisaggregator",
    "GridOperations",
    "ConfigManager",
    "ValidationUtils",
    "__version__",
]

# Package metadata
__title__ = "prism-hourly-dataset"
__description__ = "Create hourly precipitation datasets by combining daily PRISM data with NCEP Stage IV radar data"
__url__ = "https://github.com/your-org/prism-hourly-dataset"
__doc__ = __doc__
