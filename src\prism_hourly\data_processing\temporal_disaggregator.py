"""
Temporal disaggregation module for creating hourly precipitation estimates.

This module implements the core temporal disaggregation algorithm that combines
daily PRISM precipitation totals with hourly NCEP Stage IV radar patterns to
create hourly precipitation estimates that preserve the PRISM daily totals.
"""

import os
from pathlib import Path
from typing import List, Optional, Dict, Tuple
import logging
from datetime import datetime, timedelta

import numpy as np
import xarray as xr
import pandas as pd
from tqdm import tqdm

from ..utils.logging_utils import LoggingUtils
from ..utils.validation_utils import ValidationUtils


class TemporalDisaggregator:
    """
    Create hourly precipitation estimates using temporal disaggregation.
    
    This class implements a temporal disaggregation methodology that:
    1. Uses PRISM daily totals as the "truth" for spatial accuracy
    2. Uses NCEP Stage IV radar data to provide hourly temporal patterns
    3. Ensures mass conservation (hourly estimates sum to daily totals)
    4. Handles missing data and quality control
    
    Attributes:
        prism_dir (Path): Directory containing processed PRISM daily files
        ncep_dir (Path): Directory containing processed NCEP hourly files
        output_dir (Path): Directory to save hourly estimates
        hour_range (Tuple[int, int]): Hour range for disaggregation (start, end)
        logger (logging.Logger): Logger instance for this class
    
    Example:
        >>> disaggregator = TemporalDisaggregator(
        ...     prism_dir="./data/prism_processed",
        ...     ncep_dir="./data/ncep_processed",
        ...     output_dir="./data/prism_hourly"
        ... )
        >>> stats = disaggregator.process_all_prism_files()
    """
    
    def __init__(
        self,
        prism_dir: str,
        ncep_dir: str,
        output_dir: str,
        hour_range: Tuple[int, int] = (12, 36),
        quality_control: bool = True,
        mass_conservation_tolerance: float = 0.01,
        fill_missing_hours: bool = True
    ):
        """
        Initialize the temporal disaggregator.
        
        Args:
            prism_dir: Directory containing processed PRISM daily files
            ncep_dir: Directory containing processed NCEP hourly files
            output_dir: Directory to save hourly estimates
            hour_range: Hour range for disaggregation (start_hour, end_hour)
            quality_control: Whether to perform quality control checks
            mass_conservation_tolerance: Tolerance for mass conservation check (fraction)
            fill_missing_hours: Whether to fill missing hourly data with zeros
        """
        self.prism_dir = Path(prism_dir)
        self.ncep_dir = Path(ncep_dir)
        self.output_dir = Path(output_dir)
        self.hour_range = hour_range
        self.quality_control = quality_control
        self.mass_conservation_tolerance = mass_conservation_tolerance
        self.fill_missing_hours = fill_missing_hours
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Set up logging
        self.logger = LoggingUtils.get_logger(__name__)
        
        self.logger.info(f"Initialized temporal disaggregator")
        self.logger.info(f"Hour range: {hour_range[0]}Z to {hour_range[1]}Z")
    
    def process_all_prism_files(self) -> Dict[str, int]:
        """
        Process all PRISM files in the input directory.
        
        Returns:
            Dictionary with processing statistics
        """
        prism_files = sorted(self.prism_dir.glob("PRISM_*.nc"))
        
        if not prism_files:
            self.logger.warning(f"No PRISM files found in {self.prism_dir}")
            return {"processed": 0, "failed": 0, "skipped": 0}
        
        self.logger.info(f"Processing {len(prism_files)} PRISM files")
        
        stats = {"processed": 0, "failed": 0, "skipped": 0}
        
        with tqdm(total=len(prism_files), desc="Creating hourly datasets") as pbar:
            for prism_file in prism_files:
                try:
                    result = self.process_prism_file(prism_file)
                    if result:
                        if result == "skipped":
                            stats["skipped"] += 1
                        else:
                            stats["processed"] += 1
                    else:
                        stats["failed"] += 1
                        
                    pbar.set_postfix({
                        "Processed": stats["processed"],
                        "Failed": stats["failed"],
                        "Skipped": stats["skipped"]
                    })
                    
                except Exception as e:
                    self.logger.error(f"Error processing {prism_file}: {e}")
                    stats["failed"] += 1
                
                pbar.update(1)
        
        self.logger.info(f"Processing complete: {stats}")
        return stats
    
    def process_prism_file(self, prism_file: Path) -> Optional[str]:
        """
        Process a single PRISM file to create hourly estimates.
        
        Args:
            prism_file: Path to PRISM daily file
            
        Returns:
            "success" if processed, "skipped" if skipped, None if failed
        """
        try:
            # Extract date from filename
            date_str = prism_file.stem.split('_')[1]  # PRISM_YYYYMMDD.nc
            prism_date = datetime.strptime(date_str, '%Y%m%d')
            
        except (IndexError, ValueError) as e:
            self.logger.error(f"Could not extract date from {prism_file}: {e}")
            return None
        
        # Check if output files already exist
        if self._check_output_exists(prism_date):
            self.logger.debug(f"Output already exists for {date_str}")
            return "skipped"
        
        # Get corresponding NCEP files
        ncep_files = self._get_ncep_files_for_date(prism_date)
        
        if not ncep_files:
            self.logger.warning(f"No NCEP files found for {date_str}")
            return None
        
        try:
            # Load PRISM daily data
            daily_ds = xr.open_dataset(prism_file)
            daily_total = daily_ds['precipitation']
            
            # Load NCEP hourly data
            ncep_datasets = []
            valid_hours = []
            
            for hour, ncep_file in ncep_files:
                if ncep_file and ncep_file.exists():
                    try:
                        ncep_ds = xr.open_dataset(ncep_file)
                        ncep_datasets.append(ncep_ds['precipitation'])
                        valid_hours.append(hour)
                    except Exception as e:
                        self.logger.warning(f"Could not load {ncep_file}: {e}")
                        if self.fill_missing_hours:
                            # Create zero array with same dimensions
                            zero_data = xr.zeros_like(daily_total.isel(time=0))
                            ncep_datasets.append(zero_data)
                            valid_hours.append(hour)
                elif self.fill_missing_hours:
                    # Create zero array for missing hour
                    zero_data = xr.zeros_like(daily_total.isel(time=0))
                    ncep_datasets.append(zero_data)
                    valid_hours.append(hour)
            
            if not ncep_datasets:
                self.logger.error(f"No valid NCEP data for {date_str}")
                return None
            
            # Perform temporal disaggregation
            hourly_estimates = self._perform_disaggregation(
                daily_total, ncep_datasets, valid_hours, prism_date
            )
            
            # Quality control
            if self.quality_control:
                if not self._validate_mass_conservation(daily_total, hourly_estimates):
                    self.logger.error(f"Mass conservation check failed for {date_str}")
                    return None
            
            # Save hourly estimates
            self._save_hourly_estimates(hourly_estimates, valid_hours, prism_date)
            
            # Clean up
            daily_ds.close()
            for ds in ncep_datasets:
                if hasattr(ds, 'close'):
                    ds.close()
            
            return "success"
            
        except Exception as e:
            self.logger.error(f"Error in disaggregation for {date_str}: {e}")
            return None
    
    def _get_ncep_files_for_date(self, prism_date: datetime) -> List[Tuple[int, Optional[Path]]]:
        """
        Get NCEP files corresponding to a PRISM date.
        
        Args:
            prism_date: Date of PRISM data
            
        Returns:
            List of tuples (hour, file_path) for the specified hour range
        """
        ncep_files = []
        
        for hour in range(self.hour_range[0], self.hour_range[1]):
            # Calculate the NCEP date and hour
            ncep_datetime = prism_date + timedelta(hours=hour)
            ncep_date_str = ncep_datetime.strftime("%Y%m%d%H")
            
            # Look for NCEP file
            ncep_pattern = f"*{ncep_date_str}*.nc"
            matching_files = list(self.ncep_dir.glob(ncep_pattern))
            
            if matching_files:
                ncep_files.append((hour, matching_files[0]))
            else:
                ncep_files.append((hour, None))
        
        return ncep_files
    
    def _perform_disaggregation(
        self,
        daily_total: xr.DataArray,
        ncep_datasets: List[xr.DataArray],
        valid_hours: List[int],
        prism_date: datetime
    ) -> List[xr.DataArray]:
        """
        Perform the temporal disaggregation calculation.
        
        Args:
            daily_total: PRISM daily precipitation total
            ncep_datasets: List of NCEP hourly datasets
            valid_hours: List of valid hours
            prism_date: Date of PRISM data
            
        Returns:
            List of hourly precipitation estimates
        """
        # Stack hourly NCEP data
        ncep_hourly_stack = xr.concat(ncep_datasets, dim='hour')
        
        # Calculate total NCEP precipitation for the day
        ncep_daily_sum = ncep_hourly_stack.sum(dim='hour')
        
        # Avoid division by zero
        ncep_daily_sum = xr.where(ncep_daily_sum == 0, np.nan, ncep_daily_sum)
        
        # Calculate hourly ratios
        hourly_estimates = []
        
        for i, hour in enumerate(valid_hours):
            # Calculate ratio for this hour
            hourly_ratio = ncep_datasets[i] / ncep_daily_sum
            
            # Apply ratio to PRISM daily total
            hourly_estimate = hourly_ratio * daily_total.isel(time=0)
            
            # Fill NaN values with 0 (where NCEP sum was 0)
            hourly_estimate = hourly_estimate.fillna(0.0)
            
            # Ensure non-negative values
            hourly_estimate = xr.where(hourly_estimate < 0, 0.0, hourly_estimate)
            
            hourly_estimates.append(hourly_estimate)
        
        return hourly_estimates
    
    def _validate_mass_conservation(
        self,
        daily_total: xr.DataArray,
        hourly_estimates: List[xr.DataArray]
    ) -> bool:
        """
        Validate that hourly estimates sum to daily total (mass conservation).
        
        Args:
            daily_total: Original PRISM daily total
            hourly_estimates: List of hourly estimates
            
        Returns:
            True if mass conservation is satisfied within tolerance
        """
        # Sum hourly estimates
        hourly_sum = sum(hourly_estimates)
        
        # Calculate relative difference
        daily_values = daily_total.isel(time=0).values
        hourly_sum_values = hourly_sum.values
        
        # Mask for non-zero daily values
        mask = daily_values > 0
        
        if not np.any(mask):
            return True  # No precipitation, conservation trivially satisfied
        
        # Calculate relative error
        relative_error = np.abs(
            (hourly_sum_values[mask] - daily_values[mask]) / daily_values[mask]
        )
        
        max_error = np.max(relative_error)
        mean_error = np.mean(relative_error)
        
        self.logger.debug(f"Mass conservation - Max error: {max_error:.4f}, Mean error: {mean_error:.4f}")
        
        return max_error <= self.mass_conservation_tolerance
    
    def _check_output_exists(self, prism_date: datetime) -> bool:
        """Check if output files already exist for the given date."""
        date_str = prism_date.strftime("%Y%m%d")
        
        # Check for any hourly files for this date
        pattern = f"PRISM_{date_str}*.nc"
        existing_files = list(self.output_dir.glob(pattern))
        
        return len(existing_files) > 0
    
    def _save_hourly_estimates(
        self,
        hourly_estimates: List[xr.DataArray],
        valid_hours: List[int],
        prism_date: datetime
    ):
        """Save hourly estimates to NetCDF files."""
        for i, (estimate, hour) in enumerate(zip(hourly_estimates, valid_hours)):
            # Calculate actual datetime for this hour
            hour_datetime = prism_date + timedelta(hours=hour)
            
            # Create filename
            filename = f"PRISM_{hour_datetime.strftime('%Y%m%d%H')}.nc"
            output_path = self.output_dir / filename
            
            # Create dataset
            dataset = xr.Dataset(
                {
                    'precipitation': (
                        ['time', 'lat', 'lon'],
                        estimate.values[np.newaxis, :, :],
                        {
                            'units': 'mm',
                            'long_name': 'Hourly precipitation estimate',
                            'standard_name': 'precipitation_amount',
                            '_FillValue': -9999.0
                        }
                    )
                },
                coords={
                    'time': ('time', [hour_datetime]),
                    'lat': ('lat', estimate.lat.values),
                    'lon': ('lon', estimate.lon.values)
                },
                attrs={
                    'title': 'PRISM Hourly Precipitation Estimate',
                    'source': 'Temporal disaggregation of PRISM daily and NCEP Stage IV hourly data',
                    'method': 'Temporal disaggregation preserving daily mass',
                    'creation_date': datetime.now().isoformat(),
                    'prism_date': prism_date.strftime('%Y-%m-%d'),
                    'hour_utc': hour
                }
            )
            
            # Save with compression
            encoding = {
                'precipitation': {
                    'zlib': True,
                    'complevel': 6,
                    'shuffle': True,
                    '_FillValue': -9999.0
                }
            }
            
            dataset.to_netcdf(output_path, encoding=encoding)
            dataset.close()
