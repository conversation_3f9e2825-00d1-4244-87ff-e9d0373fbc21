# Include package metadata
include README.md
include LICENSE
include CHANGELOG.md
include requirements.txt
include requirements-dev.txt

# Include configuration files
recursive-include config *
recursive-include src/prism_hourly/config *

# Include documentation
recursive-include docs *

# Include examples
recursive-include examples *

# Include tests
recursive-include tests *

# Exclude development files
exclude .gitignore
exclude .pre-commit-config.yaml
exclude pyproject.toml
exclude setup.cfg
exclude tox.ini

# Exclude build artifacts
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude .git*
global-exclude .coverage
global-exclude .pytest_cache
