"""
PRISM data downloader module.

This module provides functionality for downloading daily precipitation data from the
PRISM Climate Group at Oregon State University.
"""

import datetime
import time
import subprocess
import os
from pathlib import Path
from typing import Optional, List, Tuple
import logging
from urllib.parse import urljoin
import requests
from tqdm import tqdm

from ..utils.logging_utils import LoggingUtils


class PRISMDownloader:
    """
    Download PRISM daily precipitation data.
    
    The PRISM (Parameter-elevation Regressions on Independent Slopes Model) dataset
    provides high-quality spatial climate data for the United States.
    
    Attributes:
        base_url (str): Base URL for PRISM data downloads
        output_dir (Path): Directory to save downloaded files
        delay_seconds (float): Delay between downloads to be respectful to server
        logger (logging.Logger): Logger instance for this class
    
    Example:
        >>> downloader = PRISMDownloader(output_dir="./data/prism_raw")
        >>> downloader.download_date_range(
        ...     datetime.date(2023, 1, 1), 
        ...     datetime.date(2023, 1, 31)
        ... )
    """
    
    def __init__(
        self,
        output_dir: str = "./data/prism_raw",
        base_url: str = "https://data.prism.oregonstate.edu/daily/ppt",
        delay_seconds: float = 2.0,
        max_retries: int = 3,
        timeout: int = 300
    ):
        """
        Initialize the PRISM downloader.
        
        Args:
            output_dir: Directory to save downloaded files
            base_url: Base URL for PRISM data
            delay_seconds: Delay between downloads (be respectful to server)
            max_retries: Maximum number of retry attempts for failed downloads
            timeout: Timeout for download requests in seconds
        """
        self.base_url = base_url
        self.output_dir = Path(output_dir)
        self.delay_seconds = delay_seconds
        self.max_retries = max_retries
        self.timeout = timeout
        
        # Create output directory if it doesn't exist
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Set up logging
        self.logger = LoggingUtils.get_logger(__name__)
        
    def download_date_range(
        self, 
        start_date: datetime.date, 
        end_date: datetime.date,
        variable: str = "ppt"
    ) -> List[str]:
        """
        Download PRISM data for a date range.
        
        Args:
            start_date: Start date for downloads
            end_date: End date for downloads (inclusive)
            variable: PRISM variable to download (default: "ppt" for precipitation)
            
        Returns:
            List of successfully downloaded file paths
            
        Raises:
            ValueError: If start_date is after end_date
        """
        if start_date > end_date:
            raise ValueError("start_date must be before or equal to end_date")
            
        self.logger.info(f"Starting PRISM download from {start_date} to {end_date}")
        
        downloaded_files = []
        current_date = start_date
        
        # Calculate total days for progress bar
        total_days = (end_date - start_date).days + 1
        
        with tqdm(total=total_days, desc="Downloading PRISM data") as pbar:
            while current_date <= end_date:
                try:
                    file_path = self.download_single_date(current_date, variable)
                    if file_path:
                        downloaded_files.append(file_path)
                        pbar.set_postfix({"Last": current_date.strftime("%Y-%m-%d")})
                except Exception as e:
                    self.logger.error(f"Failed to download {current_date}: {e}")
                
                current_date += datetime.timedelta(days=1)
                pbar.update(1)
                
                # Be respectful to the server
                if current_date <= end_date:
                    time.sleep(self.delay_seconds)
        
        self.logger.info(f"Downloaded {len(downloaded_files)} files successfully")
        return downloaded_files
    
    def download_single_date(
        self, 
        date: datetime.date, 
        variable: str = "ppt"
    ) -> Optional[str]:
        """
        Download PRISM data for a single date.
        
        Args:
            date: Date to download
            variable: PRISM variable to download
            
        Returns:
            Path to downloaded file if successful, None otherwise
        """
        year = date.year
        day_str = date.strftime('%Y%m%d')
        filename = f"PRISM_{variable}_stable_4kmD2_{day_str}_bil.zip"
        url = f"{self.base_url}/{year}/{filename}"
        
        output_path = self.output_dir / filename
        
        # Skip if file already exists
        if output_path.exists():
            self.logger.debug(f"File already exists: {filename}")
            return str(output_path)
        
        # Try downloading with retries
        for attempt in range(self.max_retries):
            try:
                self.logger.debug(f"Downloading {filename} (attempt {attempt + 1})")
                
                # Use wget if available, otherwise use requests
                if self._has_wget():
                    success = self._download_with_wget(url, output_path)
                else:
                    success = self._download_with_requests(url, output_path)
                
                if success:
                    self.logger.debug(f"Successfully downloaded {filename}")
                    return str(output_path)
                    
            except Exception as e:
                self.logger.warning(f"Download attempt {attempt + 1} failed for {filename}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
        
        self.logger.error(f"Failed to download {filename} after {self.max_retries} attempts")
        return None
    
    def _has_wget(self) -> bool:
        """Check if wget is available on the system."""
        try:
            subprocess.run(['wget', '--version'], 
                         capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def _download_with_wget(self, url: str, output_path: Path) -> bool:
        """Download file using wget."""
        try:
            result = subprocess.run([
                'wget', 
                '--content-disposition',
                '--timeout', str(self.timeout),
                '--tries', str(self.max_retries),
                '-O', str(output_path),
                url
            ], capture_output=True, text=True, check=True)
            return True
        except subprocess.CalledProcessError as e:
            self.logger.error(f"wget failed: {e.stderr}")
            return False
    
    def _download_with_requests(self, url: str, output_path: Path) -> bool:
        """Download file using requests library."""
        try:
            response = requests.get(url, timeout=self.timeout, stream=True)
            response.raise_for_status()
            
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            return True
        except requests.RequestException as e:
            self.logger.error(f"requests download failed: {e}")
            return False
    
    def verify_downloads(self, file_list: Optional[List[str]] = None) -> dict:
        """
        Verify downloaded files.
        
        Args:
            file_list: List of files to verify. If None, verify all files in output_dir
            
        Returns:
            Dictionary with verification results
        """
        if file_list is None:
            file_list = list(self.output_dir.glob("PRISM_*.zip"))
        
        results = {
            "total": len(file_list),
            "valid": 0,
            "invalid": 0,
            "missing": 0,
            "invalid_files": []
        }
        
        for file_path in file_list:
            path = Path(file_path)
            if not path.exists():
                results["missing"] += 1
                continue
                
            # Basic validation - check if file is not empty and is a zip file
            if path.stat().st_size > 0:
                try:
                    import zipfile
                    with zipfile.ZipFile(path, 'r') as zf:
                        zf.testzip()  # Test zip integrity
                    results["valid"] += 1
                except zipfile.BadZipFile:
                    results["invalid"] += 1
                    results["invalid_files"].append(str(path))
            else:
                results["invalid"] += 1
                results["invalid_files"].append(str(path))
        
        self.logger.info(f"Verification complete: {results['valid']}/{results['total']} files valid")
        return results
