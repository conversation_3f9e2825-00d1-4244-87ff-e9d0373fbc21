{"tests/test_cli.py": true, "tests/test_config_manager.py::TestConfigManager::test_load_nonexistent_config": true, "tests/test_config_manager.py::TestConfigManager::test_invalid_yaml_config": true, "tests/test_config_manager.py::TestConfigManager::test_get_invalid_section": true, "tests/test_config_manager.py::TestConfigManager::test_update_config": true, "tests/test_config_manager.py::TestConfigManager::test_config_dataclass_validation": true, "tests/test_config_manager.py::TestConfigManager::test_config_validation_errors": true, "tests/test_config_manager.py::TestConfigManager::test_config_type_conversion": true, "tests/test_data_processor.py::TestDataProcessor::test_init_default_parameters": true, "tests/test_data_processor.py::TestDataProcessor::test_init_custom_parameters": true, "tests/test_data_processor.py::TestDataProcessor::test_process_prism_file_valid": true, "tests/test_data_processor.py::TestDataProcessor::test_process_prism_file_invalid_input": true, "tests/test_data_processor.py::TestDataProcessor::test_process_ncep_file_valid": true, "tests/test_data_processor.py::TestDataProcessor::test_reproject_to_grid": true, "tests/test_data_processor.py::TestDataProcessor::test_read_bil_file": true, "tests/test_data_processor.py::TestDataProcessor::test_convert_to_netcdf": true, "tests/test_data_processor.py::TestDataProcessor::test_apply_quality_control": true, "tests/test_data_processor.py::TestDataProcessor::test_validate_output": true, "tests/test_data_processor.py::TestDataProcessor::test_processing_with_compression": true, "tests/test_data_processor.py::TestDataProcessor::test_processing_without_compression": true, "tests/test_data_processor.py::TestDataProcessor::test_error_handling_invalid_resolution": true, "tests/test_data_processor.py::TestDataProcessor::test_error_handling_invalid_resampling_method": true, "tests/test_data_processor.py::TestDataProcessor::test_memory_efficient_processing": true, "tests/test_data_processor.py::TestDataProcessor::test_parallel_processing_capability": true, "tests/test_integration.py::TestIntegration::test_config_to_processor_integration": true, "tests/test_integration.py::TestIntegration::test_config_to_disaggregator_integration": true, "tests/test_integration.py::TestIntegration::test_processor_to_validator_integration": true, "tests/test_integration.py::TestIntegration::test_mass_conservation_integration": true, "tests/test_integration.py::TestIntegration::test_grid_consistency_integration": true, "tests/test_integration.py::TestIntegration::test_configuration_validation_integration": true, "tests/test_integration.py::TestIntegration::test_error_propagation_integration": true, "tests/test_integration.py::TestIntegration::test_performance_integration": true, "tests/test_integration.py::TestIntegration::test_memory_usage_integration": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_init_default_parameters": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_init_custom_parameters": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_build_download_url": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_build_download_url_different_dates": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_download_file_success": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_download_file_http_error": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_download_file_with_retries": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_download_file_max_retries_exceeded": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_download_delay_applied": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_download_multiple_files": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_download_multiple_files_empty_list": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_validate_date_valid": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_validate_date_invalid": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_validate_output_directory_valid": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_validate_output_directory_invalid": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_validate_output_directory_not_writable": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_download_with_progress_callback": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_get_filename_for_date": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_download_with_custom_headers": true, "tests/test_prism_downloader.py::TestPRISMDownloader::test_error_handling_invalid_parameters": true, "tests/test_temporal_disaggregator.py::TestTemporalDisaggregator::test_init_default_parameters": true, "tests/test_temporal_disaggregator.py::TestTemporalDisaggregator::test_init_custom_parameters": true, "tests/test_temporal_disaggregator.py::TestTemporalDisaggregator::test_disaggregate_basic": true, "tests/test_temporal_disaggregator.py::TestTemporalDisaggregator::test_disaggregate_with_date_range": true, "tests/test_temporal_disaggregator.py::TestTemporalDisaggregator::test_calculate_temporal_weights": true, "tests/test_temporal_disaggregator.py::TestTemporalDisaggregator::test_apply_weights_to_daily": true, "tests/test_temporal_disaggregator.py::TestTemporalDisaggregator::test_validate_mass_conservation": true, "tests/test_temporal_disaggregator.py::TestTemporalDisaggregator::test_validate_mass_conservation_violation": true, "tests/test_temporal_disaggregator.py::TestTemporalDisaggregator::test_interpolate_missing_hours": true, "tests/test_temporal_disaggregator.py::TestTemporalDisaggregator::test_quality_control_application": true, "tests/test_temporal_disaggregator.py::TestTemporalDisaggregator::test_grid_alignment_check": true, "tests/test_temporal_disaggregator.py::TestTemporalDisaggregator::test_grid_alignment_mismatch": true, "tests/test_temporal_disaggregator.py::TestTemporalDisaggregator::test_error_handling_invalid_tolerance": true, "tests/test_temporal_disaggregator.py::TestTemporalDisaggregator::test_error_handling_invalid_hour_range": true, "tests/test_temporal_disaggregator.py::TestTemporalDisaggregator::test_error_handling_invalid_interpolation_method": true, "tests/test_temporal_disaggregator.py::TestTemporalDisaggregator::test_performance_large_dataset": true, "tests/test_validation_utils.py::TestValidationUtils::test_validate_file_integrity_valid": true, "tests/test_validation_utils.py::TestValidationUtils::test_validate_file_integrity_nonexistent": true, "tests/test_validation_utils.py::TestValidationUtils::test_validate_spatial_consistency": true, "tests/test_validation_utils.py::TestValidationUtils::test_validate_temporal_consistency": true, "tests/test_validation_utils.py::TestValidationUtils::test_validate_data_ranges": true, "tests/test_validation_utils.py::TestValidationUtils::test_validate_metadata": true, "tests/test_validation_utils.py::TestValidationUtils::test_validate_directory_structure": true, "tests/test_validation_utils.py::TestValidationUtils::test_validate_grid_consistency_matching": true, "tests/test_validation_utils.py::TestValidationUtils::test_validate_grid_consistency_mismatched": true, "tests/test_validation_utils.py::TestValidationUtils::test_validate_coordinate_system": true, "tests/test_validation_utils.py::TestValidationUtils::test_custom_validation_tolerances": true, "tests/test_validation_utils.py::TestValidationUtils::test_validation_error_reporting": true}