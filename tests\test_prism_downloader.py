"""
Tests for the PRISMDownloader class.
"""

import pytest
import requests
from pathlib import Path
import tempfile
import sys
from datetime import date, datetime
from unittest.mock import Mock, patch, MagicMock

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from prism_hourly.data_download.prism_downloader import PRISMDownloader


class TestPRISMDownloader:
    """Test cases for PRISMDownloader."""
    
    def test_init_default_parameters(self):
        """Test PRISMDownloader initialization with default parameters."""
        downloader = PRISMDownloader()
        
        assert downloader.base_url == 'https://data.prism.oregonstate.edu/daily/ppt'
        assert downloader.download_delay == 2.0
        assert downloader.max_retries == 3
        assert downloader.timeout == 60
        assert 'PRISM-Hourly-Dataset' in downloader.user_agent
    
    def test_init_custom_parameters(self):
        """Test PRISMDownloader initialization with custom parameters."""
        downloader = PRISMDownloader(
            base_url='https://custom.url.com',
            download_delay=5.0,
            max_retries=5,
            timeout=120,
            user_agent='Custom-Agent/1.0'
        )
        
        assert downloader.base_url == 'https://custom.url.com'
        assert downloader.download_delay == 5.0
        assert downloader.max_retries == 5
        assert downloader.timeout == 120
        assert downloader.user_agent == 'Custom-Agent/1.0'
    
    def test_build_download_url(self):
        """Test URL building for PRISM downloads."""
        downloader = PRISMDownloader()
        
        target_date = date(2023, 7, 15)
        url = downloader._build_download_url(target_date)
        
        expected_url = 'https://data.prism.oregonstate.edu/daily/ppt/2023/PRISM_ppt_stable_4kmD2_20230715_bil.zip'
        assert url == expected_url
    
    def test_build_download_url_different_dates(self):
        """Test URL building for different dates."""
        downloader = PRISMDownloader()
        
        # Test different date formats
        test_dates = [
            (date(2023, 1, 1), '20230101'),
            (date(2023, 12, 31), '20231231'),
            (date(2020, 2, 29), '20200229'),  # Leap year
        ]
        
        for test_date, expected_date_str in test_dates:
            url = downloader._build_download_url(test_date)
            assert expected_date_str in url
            assert str(test_date.year) in url
    
    @patch('requests.get')
    def test_download_file_success(self, mock_get, temp_dir):
        """Test successful file download."""
        downloader = PRISMDownloader()
        
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {'content-length': '1024'}
        mock_response.iter_content = Mock(return_value=[b'test data chunk 1', b'test data chunk 2'])
        mock_response.raise_for_status = Mock()
        mock_get.return_value = mock_response
        
        target_date = date(2023, 7, 15)
        output_file = temp_dir / "test_download.zip"
        
        result = downloader.download_file(target_date, str(output_file))
        
        assert result == str(output_file)
        assert output_file.exists()
        
        # Verify the content
        with open(output_file, 'rb') as f:
            content = f.read()
            assert content == b'test data chunk 1test data chunk 2'
        
        # Verify the request was made correctly
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        assert 'PRISM_ppt_stable_4kmD2_20230715_bil.zip' in call_args[0][0]
    
    @patch('requests.get')
    def test_download_file_http_error(self, mock_get, temp_dir):
        """Test download with HTTP error."""
        downloader = PRISMDownloader()
        
        # Mock HTTP error response
        mock_response = Mock()
        mock_response.status_code = 404
        mock_response.raise_for_status.side_effect = requests.HTTPError("404 Not Found")
        mock_get.return_value = mock_response
        
        target_date = date(2023, 7, 15)
        output_file = temp_dir / "test_download.zip"
        
        with pytest.raises(requests.HTTPError):
            downloader.download_file(target_date, str(output_file))
    
    @patch('requests.get')
    def test_download_file_with_retries(self, mock_get, temp_dir):
        """Test download with retries on failure."""
        downloader = PRISMDownloader(max_retries=3)
        
        # Mock responses: first two fail, third succeeds
        mock_responses = [
            Mock(status_code=500, raise_for_status=Mock(side_effect=requests.HTTPError("500 Server Error"))),
            Mock(status_code=503, raise_for_status=Mock(side_effect=requests.HTTPError("503 Service Unavailable"))),
            Mock(status_code=200, headers={'content-length': '100'}, 
                 iter_content=Mock(return_value=[b'success data']), raise_for_status=Mock())
        ]
        mock_get.side_effect = mock_responses
        
        target_date = date(2023, 7, 15)
        output_file = temp_dir / "test_download.zip"
        
        result = downloader.download_file(target_date, str(output_file))
        
        assert result == str(output_file)
        assert output_file.exists()
        assert mock_get.call_count == 3  # Two failures + one success
    
    @patch('requests.get')
    def test_download_file_max_retries_exceeded(self, mock_get, temp_dir):
        """Test download when max retries are exceeded."""
        downloader = PRISMDownloader(max_retries=2)
        
        # Mock all responses to fail
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.raise_for_status.side_effect = requests.HTTPError("500 Server Error")
        mock_get.return_value = mock_response
        
        target_date = date(2023, 7, 15)
        output_file = temp_dir / "test_download.zip"
        
        with pytest.raises(requests.HTTPError):
            downloader.download_file(target_date, str(output_file))
        
        assert mock_get.call_count == 3  # Initial attempt + 2 retries
    
    @patch('requests.get')
    @patch('time.sleep')
    def test_download_delay_applied(self, mock_sleep, mock_get, temp_dir):
        """Test that download delay is applied between retries."""
        downloader = PRISMDownloader(download_delay=1.0, max_retries=2)
        
        # Mock responses: first fails, second succeeds
        mock_responses = [
            Mock(status_code=500, raise_for_status=Mock(side_effect=requests.HTTPError("500 Server Error"))),
            Mock(status_code=200, headers={'content-length': '100'}, 
                 iter_content=Mock(return_value=[b'success data']), raise_for_status=Mock())
        ]
        mock_get.side_effect = mock_responses
        
        target_date = date(2023, 7, 15)
        output_file = temp_dir / "test_download.zip"
        
        downloader.download_file(target_date, str(output_file))
        
        # Verify sleep was called with correct delay
        mock_sleep.assert_called_with(1.0)
    
    @patch('requests.get')
    def test_download_multiple_files(self, mock_get, temp_dir):
        """Test downloading multiple files."""
        downloader = PRISMDownloader()
        
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {'content-length': '1024'}
        mock_response.iter_content = Mock(return_value=[b'test data'])
        mock_response.raise_for_status = Mock()
        mock_get.return_value = mock_response
        
        dates = [date(2023, 7, 1), date(2023, 7, 2), date(2023, 7, 3)]
        
        results = downloader.download_multiple_files(dates, str(temp_dir))
        
        assert len(results) == 3
        for result in results:
            assert Path(result).exists()
            assert result.endswith('.zip')
        
        # Verify correct number of requests
        assert mock_get.call_count == 3
    
    def test_download_multiple_files_empty_list(self, temp_dir):
        """Test downloading with empty date list."""
        downloader = PRISMDownloader()
        
        results = downloader.download_multiple_files([], str(temp_dir))
        
        assert results == []
    
    @patch('requests.get')
    def test_download_multiple_files_partial_failure(self, mock_get, temp_dir):
        """Test downloading multiple files with some failures."""
        downloader = PRISMDownloader(max_retries=1)
        
        # Mock responses: first succeeds, second fails, third succeeds
        mock_responses = [
            Mock(status_code=200, headers={'content-length': '100'}, 
                 iter_content=Mock(return_value=[b'data1']), raise_for_status=Mock()),
            Mock(status_code=404, raise_for_status=Mock(side_effect=requests.HTTPError("404 Not Found"))),
            Mock(status_code=404, raise_for_status=Mock(side_effect=requests.HTTPError("404 Not Found"))),  # Retry
            Mock(status_code=200, headers={'content-length': '100'}, 
                 iter_content=Mock(return_value=[b'data3']), raise_for_status=Mock())
        ]
        mock_get.side_effect = mock_responses
        
        dates = [date(2023, 7, 1), date(2023, 7, 2), date(2023, 7, 3)]
        
        # Should not raise exception, but return partial results
        try:
            results = downloader.download_multiple_files(dates, str(temp_dir))
            # Some files should succeed
            successful_downloads = [r for r in results if Path(r).exists()]
            assert len(successful_downloads) >= 1
        except Exception:
            # Partial failure handling depends on implementation
            pass
    
    def test_validate_date_valid(self):
        """Test date validation with valid dates."""
        downloader = PRISMDownloader()
        
        valid_dates = [
            date(2023, 7, 15),
            date(2020, 2, 29),  # Leap year
            date(1981, 1, 1),   # PRISM start date
        ]
        
        for valid_date in valid_dates:
            # Should not raise exception
            downloader._validate_date(valid_date)
    
    def test_validate_date_invalid(self):
        """Test date validation with invalid dates."""
        downloader = PRISMDownloader()
        
        # Test future date
        future_date = date(2030, 1, 1)
        with pytest.raises(ValueError):
            downloader._validate_date(future_date)
        
        # Test date before PRISM data availability
        too_early_date = date(1980, 1, 1)
        with pytest.raises(ValueError):
            downloader._validate_date(too_early_date)
    
    def test_validate_output_directory_valid(self, temp_dir):
        """Test output directory validation with valid directory."""
        downloader = PRISMDownloader()
        
        # Should not raise exception
        downloader._validate_output_directory(str(temp_dir))
    
    def test_validate_output_directory_invalid(self):
        """Test output directory validation with invalid directory."""
        downloader = PRISMDownloader()
        
        # Test non-existent directory
        with pytest.raises(ValueError):
            downloader._validate_output_directory("/nonexistent/directory")
    
    def test_validate_output_directory_not_writable(self, temp_dir):
        """Test output directory validation with non-writable directory."""
        downloader = PRISMDownloader()
        
        # Create a directory and make it read-only (if possible)
        readonly_dir = temp_dir / "readonly"
        readonly_dir.mkdir()
        
        try:
            readonly_dir.chmod(0o444)  # Read-only
            
            with pytest.raises(ValueError):
                downloader._validate_output_directory(str(readonly_dir))
                
        except (OSError, PermissionError):
            # Skip test if we can't change permissions
            pytest.skip("Cannot change directory permissions on this system")
        finally:
            # Restore permissions for cleanup
            try:
                readonly_dir.chmod(0o755)
            except (OSError, PermissionError):
                pass
    
    @patch('requests.get')
    def test_download_with_progress_callback(self, mock_get, temp_dir):
        """Test download with progress callback."""
        downloader = PRISMDownloader()
        
        # Mock response with content
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {'content-length': '1000'}
        mock_response.iter_content = Mock(return_value=[b'x' * 100] * 10)  # 10 chunks of 100 bytes
        mock_response.raise_for_status = Mock()
        mock_get.return_value = mock_response
        
        # Track progress calls
        progress_calls = []
        def progress_callback(downloaded, total):
            progress_calls.append((downloaded, total))
        
        target_date = date(2023, 7, 15)
        output_file = temp_dir / "test_download.zip"
        
        try:
            result = downloader.download_file(
                target_date, 
                str(output_file), 
                progress_callback=progress_callback
            )
            
            assert result == str(output_file)
            assert len(progress_calls) > 0
            
            # Check that progress was reported correctly
            final_call = progress_calls[-1]
            assert final_call[0] == final_call[1]  # Downloaded == Total at the end
            
        except TypeError:
            # Progress callback might not be implemented
            pytest.skip("Progress callback not implemented")
    
    def test_get_filename_for_date(self):
        """Test filename generation for dates."""
        downloader = PRISMDownloader()
        
        test_cases = [
            (date(2023, 7, 15), 'PRISM_ppt_stable_4kmD2_20230715_bil.zip'),
            (date(2023, 1, 1), 'PRISM_ppt_stable_4kmD2_20230101_bil.zip'),
            (date(2023, 12, 31), 'PRISM_ppt_stable_4kmD2_20231231_bil.zip'),
        ]
        
        for test_date, expected_filename in test_cases:
            filename = downloader._get_filename_for_date(test_date)
            assert filename == expected_filename
    
    @patch('requests.get')
    def test_download_with_custom_headers(self, mock_get, temp_dir):
        """Test download with custom headers."""
        downloader = PRISMDownloader(user_agent='Custom-Agent/2.0')
        
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {'content-length': '100'}
        mock_response.iter_content = Mock(return_value=[b'test data'])
        mock_response.raise_for_status = Mock()
        mock_get.return_value = mock_response
        
        target_date = date(2023, 7, 15)
        output_file = temp_dir / "test_download.zip"
        
        downloader.download_file(target_date, str(output_file))
        
        # Verify custom user agent was used
        call_args = mock_get.call_args
        headers = call_args[1].get('headers', {})
        assert headers.get('User-Agent') == 'Custom-Agent/2.0'
    
    def test_error_handling_invalid_parameters(self):
        """Test error handling for invalid initialization parameters."""
        # Test invalid download delay
        with pytest.raises(ValueError):
            PRISMDownloader(download_delay=-1.0)
        
        # Test invalid max retries
        with pytest.raises(ValueError):
            PRISMDownloader(max_retries=-1)
        
        # Test invalid timeout
        with pytest.raises(ValueError):
            PRISMDownloader(timeout=0)
