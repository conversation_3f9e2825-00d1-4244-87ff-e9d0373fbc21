{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PRISM Hourly Dataset Creation - Basic Usage\n", "\n", "This notebook demonstrates the basic usage of the PRISM Hourly Dataset package for creating hourly precipitation estimates from daily PRISM and hourly NCEP Stage IV data.\n", "\n", "## Overview\n", "\n", "The package implements temporal disaggregation to:\n", "- Use PRISM daily precipitation as the spatial \"truth\"\n", "- Use NCEP Stage IV radar data for hourly temporal patterns\n", "- Ensure mass conservation (hourly estimates sum to daily totals)\n", "- Provide comprehensive quality control"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "from pathlib import Path\n", "import numpy as np\n", "import pandas as pd\n", "import xarray as xr\n", "import matplotlib.pyplot as plt\n", "from datetime import date, datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Add the package to the path\n", "sys.path.insert(0, str(Path.cwd().parent / \"src\"))\n", "\n", "# Import package modules\n", "from prism_hourly.utils.config_manager import ConfigManager\n", "from prism_hourly.utils.logging_utils import LoggingUtils\n", "from prism_hourly.data_download.prism_downloader import PRISMDownloader\n", "from prism_hourly.data_download.ncep_downloader import NCEPDownloader\n", "from prism_hourly.data_processing.data_processor import DataProcessor\n", "from prism_hourly.data_processing.temporal_disaggregator import TemporalDisaggregator\n", "from prism_hourly.utils.validation_utils import ValidationUtils\n", "\n", "print(\"Package imports successful!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configuration Management"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load configuration\n", "config_file = Path.cwd().parent / \"config\" / \"config.yaml\"\n", "config_manager = ConfigManager(str(config_file))\n", "config = config_manager.get_config()\n", "\n", "# Display configuration\n", "print(\"Configuration loaded successfully!\")\n", "print(f\"PRISM base URL: {config.download.prism_base_url}\")\n", "print(f\"Target resolution: {config.processing.resolution}m\")\n", "print(f\"Hour range: {config.disaggregation.hour_range_start}-{config.disaggregation.hour_range_end}\")\n", "\n", "# Validate configuration\n", "is_valid = config_manager.validate_config()\n", "print(f\"Configuration is valid: {is_valid}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup Logging"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configure logging for the notebook\n", "LoggingUtils.configure_logging(\n", "    level='INFO',\n", "    console_output=True,\n", "    log_file='notebook_example.log'\n", ")\n", "\n", "logger = LoggingUtils.get_logger(__name__)\n", "logger.info(\"Notebook logging configured\")\n", "\n", "print(\"Logging configured - check 'notebook_example.log' for detailed logs\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create Output Directories"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create directory structure\n", "data_dir = Path(\"./notebook_data\")\n", "prism_raw_dir = data_dir / \"prism_raw\"\n", "ncep_raw_dir = data_dir / \"ncep_raw\"\n", "prism_processed_dir = data_dir / \"prism_processed\"\n", "ncep_processed_dir = data_dir / \"ncep_processed\"\n", "hourly_output_dir = data_dir / \"prism_hourly\"\n", "\n", "# Create directories\n", "for directory in [prism_raw_dir, ncep_raw_dir, prism_processed_dir, \n", "                 ncep_processed_dir, hourly_output_dir]:\n", "    directory.mkdir(parents=True, exist_ok=True)\n", "    print(f\"Created: {directory}\")\n", "\n", "print(f\"\\nAll directories created under: {data_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Download\n", "\n", "### Download PRISM Daily Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize PRISM downloader\n", "prism_downloader = PRISMDownloader(\n", "    output_dir=str(prism_raw_dir),\n", "    delay_seconds=1.0,  # Reduced delay for example\n", "    max_retries=2\n", ")\n", "\n", "# Download data for a small date range (adjust as needed)\n", "start_date = date(2023, 7, 1)\n", "end_date = date(2023, 7, 3)\n", "\n", "print(f\"Downloading PRISM data from {start_date} to {end_date}\")\n", "print(\"Note: This may take a few minutes depending on your internet connection\")\n", "\n", "try:\n", "    downloaded_files = prism_downloader.download_date_range(start_date, end_date)\n", "    print(f\"Downloaded {len(downloaded_files)} PRISM files\")\n", "    \n", "    # Verify downloads\n", "    verification_results = prism_downloader.verify_downloads(downloaded_files)\n", "    print(f\"Verification: {verification_results['valid']}/{verification_results['total']} files valid\")\n", "    \n", "    # Display downloaded files\n", "    for file_path in downloaded_files[:5]:  # Show first 5 files\n", "        print(f\"  - {Path(file_path).name}\")\n", "        \n", "except Exception as e:\n", "    print(f\"Download failed: {e}\")\n", "    print(\"This is normal if you don't have internet access or the server is unavailable\")\n", "    print(\"You can continue with the example using mock data\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Download NCEP Stage IV Data (Optional)\n", "\n", "Note: NCEP files are very large (several GB per month), so this is commented out for the example."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# NCEP downloader example (commented out due to large file sizes)\n", "print(\"NCEP Stage IV data download:\")\n", "print(\"- Files are very large (several GB per month)\")\n", "print(\"- Uncomment the code below to download NCEP data\")\n", "print(\"- For this example, we'll skip NCEP downloads\")\n", "\n", "# Uncomment to download NCEP data:\n", "# ncep_downloader = NCEPDownloader(\n", "#     output_dir=str(ncep_raw_dir),\n", "#     max_retries=2\n", "# )\n", "# \n", "# # Download for specific year\n", "# downloaded_ncep_files = ncep_downloader.download_year_range(2023, 2023)\n", "# print(f\"Downloaded {len(downloaded_ncep_files)} NCEP files\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Data Processing\n", "\n", "### Process PRISM Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for PRISM files to process\n", "prism_files = list(prism_raw_dir.glob(\"*.zip\"))\n", "print(f\"Found {len(prism_files)} PRISM files to process\")\n", "\n", "if prism_files:\n", "    # Initialize data processor\n", "    processor = DataProcessor(\n", "        resolution=config.processing.resolution,\n", "        nodata_value=config.processing.nodata_value,\n", "        resampling_method=config.processing.resampling_method\n", "    )\n", "    \n", "    print(\"Processing PRISM files...\")\n", "    processed_files = processor.process_prism_files(\n", "        str(prism_raw_dir),\n", "        str(prism_processed_dir)\n", "    )\n", "    \n", "    print(f\"Processed {len(processed_files)} files\")\n", "    \n", "    # Display processed files\n", "    for file_path in processed_files[:3]:  # Show first 3 files\n", "        print(f\"  - {Path(file_path).name}\")\n", "        \n", "else:\n", "    print(\"No PRISM files found to process\")\n", "    print(\"This is normal if the download step was skipped\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Visualization\n", "\n", "Let's visualize some of the processed data if available."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Find processed NetCDF files\n", "processed_nc_files = list(prism_processed_dir.glob(\"*.nc\"))\n", "\n", "if processed_nc_files:\n", "    # Load and visualize the first file\n", "    sample_file = processed_nc_files[0]\n", "    print(f\"Visualizing: {sample_file.name}\")\n", "    \n", "    # Load dataset\n", "    ds = xr.open_dataset(sample_file)\n", "    \n", "    # Display dataset info\n", "    print(\"\\nDataset information:\")\n", "    print(ds)\n", "    \n", "    # Create visualization\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # Plot precipitation data\n", "    precip_data = ds['precipitation'].isel(time=0)  # First time step\n", "    im1 = precip_data.plot(ax=ax1, cmap='Blues', add_colorbar=False)\n", "    ax1.set_title(f'PRISM Daily Precipitation\\n{sample_file.name}')\n", "    ax1.set_xlabel('Longitude')\n", "    ax1.set_ylabel('Latitude')\n", "    plt.colorbar(im1, ax=ax1, label='Precipitation (mm)')\n", "    \n", "    # Plot histogram\n", "    valid_data = precip_data.values[~np.isnan(precip_data.values)]\n", "    valid_data = valid_data[valid_data > 0]  # Only positive values\n", "    \n", "    if len(valid_data) > 0:\n", "        ax2.hist(valid_data, bins=50, alpha=0.7, color='blue', edgecolor='black')\n", "        ax2.set_xlabel('Precipitation (mm)')\n", "        ax2.set_ylabel('Frequency')\n", "        ax2.set_title('Precipitation Distribution')\n", "        ax2.grid(True, alpha=0.3)\n", "        \n", "        # Add statistics\n", "        mean_precip = np.mean(valid_data)\n", "        max_precip = np.max(valid_data)\n", "        ax2.axvline(mean_precip, color='red', linestyle='--', \n", "                   label=f'Mean: {mean_precip:.2f} mm')\n", "        ax2.legend()\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Display basic statistics\n", "    print(f\"\\nBasic statistics:\")\n", "    print(f\"Grid shape: {precip_data.shape}\")\n", "    print(f\"Valid data points: {len(valid_data)}\")\n", "    if len(valid_data) > 0:\n", "        print(f\"Mean precipitation: {np.mean(valid_data):.2f} mm\")\n", "        print(f\"Max precipitation: {np.max(valid_data):.2f} mm\")\n", "        print(f\"Min precipitation: {np.min(valid_data):.2f} mm\")\n", "    \n", "    ds.close()\n", "    \n", "else:\n", "    print(\"No processed NetCDF files found for visualization\")\n", "    print(\"This is normal if the processing step was skipped\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Data Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize validator\n", "validator = ValidationUtils()\n", "\n", "# Find files to validate\n", "files_to_validate = list(prism_processed_dir.glob(\"*.nc\"))\n", "\n", "if files_to_validate:\n", "    print(f\"Validating {len(files_to_validate)} files...\")\n", "    \n", "    validation_summary = {\n", "        'total': len(files_to_validate),\n", "        'valid': 0,\n", "        'invalid': 0,\n", "        'errors': [],\n", "        'warnings': []\n", "    }\n", "    \n", "    for file_path in files_to_validate[:3]:  # Validate first 3 files\n", "        print(f\"\\nValidating: {file_path.name}\")\n", "        \n", "        results = validator.validate_dataset(\n", "            str(file_path),\n", "            check_spatial=True,\n", "            check_temporal=True,\n", "            check_data_ranges=True,\n", "            check_metadata=True\n", "        )\n", "        \n", "        if results['is_valid']:\n", "            print(f\"  ✓ Valid\")\n", "            validation_summary['valid'] += 1\n", "        else:\n", "            print(f\"  ✗ Invalid\")\n", "            validation_summary['invalid'] += 1\n", "            \n", "        # Display errors and warnings\n", "        if results['errors']:\n", "            print(\"  Errors:\")\n", "            for error in results['errors']:\n", "                print(f\"    - {error}\")\n", "                validation_summary['errors'].append(error)\n", "                \n", "        if results['warnings']:\n", "            print(\"  Warnings:\")\n", "            for warning in results['warnings']:\n", "                print(f\"    - {warning}\")\n", "                validation_summary['warnings'].append(warning)\n", "        \n", "        # Display spatial info if available\n", "        if 'spatial' in results and 'grid_info' in results['spatial']:\n", "            grid_info = results['spatial']['grid_info']\n", "            print(f\"  Grid shape: {grid_info.get('shape', 'Unknown')}\")\n", "            print(f\"  Lat range: {grid_info.get('lat_range', 'Unknown')}\")\n", "            print(f\"  Lon range: {grid_info.get('lon_range', 'Unknown')}\")\n", "    \n", "    # Display validation summary\n", "    print(f\"\\n=== VALIDATION SUMMARY ===\")\n", "    print(f\"Total files: {validation_summary['total']}\")\n", "    print(f\"Valid: {validation_summary['valid']}\")\n", "    print(f\"Invalid: {validation_summary['invalid']}\")\n", "    print(f\"Total errors: {len(validation_summary['errors'])}\")\n", "    print(f\"Total warnings: {len(validation_summary['warnings'])}\")\n", "    \n", "else:\n", "    print(\"No files found for validation\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Temporal Disaggregation (Conceptual)\n", "\n", "This section shows how temporal disaggregation would work with actual data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for required data\n", "prism_processed_files = list(prism_processed_dir.glob(\"*.nc\"))\n", "ncep_processed_files = list(ncep_processed_dir.glob(\"*.nc\"))\n", "\n", "print(f\"PRISM processed files: {len(prism_processed_files)}\")\n", "print(f\"NCEP processed files: {len(ncep_processed_files)}\")\n", "\n", "if prism_processed_files and ncep_processed_files:\n", "    print(\"\\nBoth PRISM and NCEP data available - performing disaggregation\")\n", "    \n", "    # Initialize disaggregator\n", "    disaggregator = TemporalDisaggregator(\n", "        prism_dir=str(prism_processed_dir),\n", "        ncep_dir=str(ncep_processed_dir),\n", "        output_dir=str(hourly_output_dir),\n", "        hour_range=(config.disaggregation.hour_range_start, \n", "                   config.disaggregation.hour_range_end),\n", "        quality_control=True\n", "    )\n", "    \n", "    # Process files\n", "    try:\n", "        stats = disaggregator.process_all_prism_files()\n", "        print(f\"Disaggregation completed: {stats}\")\n", "        \n", "        # List output files\n", "        hourly_files = list(hourly_output_dir.glob(\"*.nc\"))\n", "        print(f\"Created {len(hourly_files)} hourly files\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Disaggregation failed: {e}\")\n", "        \n", "else:\n", "    print(\"\\nInsufficient data for temporal disaggregation\")\n", "    print(\"This example demonstrates the concept:\")\n", "    print(\"\")\n", "    print(\"1. Load PRISM daily precipitation total\")\n", "    print(\"2. Load NCEP hourly precipitation for the same day\")\n", "    print(\"3. Calculate hourly ratios from NCEP data\")\n", "    print(\"4. Apply ratios to PRISM daily total\")\n", "    print(\"5. Ensure mass conservation\")\n", "    print(\"\")\n", "    print(\"Mathematical formula:\")\n", "    print(\"hourly_estimate[h] = PRISM_daily * (NCEP_hourly[h] / NCEP_daily_sum)\")\n", "    print(\"\")\n", "    print(\"Where the sum of all hourly estimates equals the PRISM daily total.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON><PERSON> and Next Steps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== NOTEBOOK SUMMARY ===\")\n", "print(\"\")\n", "print(\"This notebook demonstrated:\")\n", "print(\"1. ✓ Package configuration and setup\")\n", "print(\"2. ✓ Data download capabilities (PRISM and NCEP)\")\n", "print(\"3. ✓ Data processing and format conversion\")\n", "print(\"4. ✓ Data visualization and analysis\")\n", "print(\"5. ✓ Comprehensive data validation\")\n", "print(\"6. ✓ Temporal disaggregation concepts\")\n", "print(\"\")\n", "print(\"Next steps:\")\n", "print(\"- Download actual PRISM and NCEP data for your region/time period\")\n", "print(\"- Run the full temporal disaggregation workflow\")\n", "print(\"- Validate mass conservation in the results\")\n", "print(\"- Explore advanced processing options\")\n", "print(\"- Check out other example notebooks:\")\n", "print(\"  * 02_advanced_processing.ipynb\")\n", "print(\"  * 03_quality_control.ipynb\")\n", "print(\"  * 04_visualization.ipynb\")\n", "print(\"\")\n", "print(\"For more information, see the README.md file and documentation.\")\n", "\n", "# Display file structure\n", "print(\"\\n=== OUTPUT FILE STRUCTURE ===\")\n", "for root, dirs, files in os.walk(data_dir):\n", "    level = root.replace(str(data_dir), '').count(os.sep)\n", "    indent = ' ' * 2 * level\n", "    print(f\"{indent}{os.path.basename(root)}/\")\n", "    subindent = ' ' * 2 * (level + 1)\n", "    for file in files[:5]:  # Show first 5 files per directory\n", "        print(f\"{subindent}{file}\")\n", "    if len(files) > 5:\n", "        print(f\"{subindent}... and {len(files) - 5} more files\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}