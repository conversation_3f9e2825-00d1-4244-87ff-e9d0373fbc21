import rasterio
from rasterio.warp import reproject, Resampling
import numpy as np
import os
from datetime import datetime, timedelta
import pytz
import xarray as xr
import matplotlib.pyplot as plt
import glob
import pandas as pd

# List of source precipitation files
source_precip_files = np.sort(glob.glob('2024/*remap.nc'))

nodata_value = -9999

# Load the NetCDF file using xarray
for file in source_precip_files:
    ds = xr.open_dataset(file)
    print(file)
    try:
        lon = ds['lon'].values
        lat = ds['lat'].values
    except:
        lon = ds['longitude'].values
        lat = ds['latitude'].values
    
    # Calculate the transform based on the lon and lat arrays
    prism_transform = rasterio.transform.from_bounds(
        lon.min(), lat.min(), lon.max(), lat.max(), len(lon), len(lat)
    )
    prism_crs = 'EPSG:4326'  # Verify CRS if necessary

    # Loop over each time step
    for i in range(ds.time.size):
        # Select data for the current time step
        try:
            prism_data = ds['tp'].isel(time=i).values
        except:
            prism_data = ds['rainrate'].isel(time=i).values
     
        nodata_value = -9999
        prism_data = np.where(prism_data == nodata_value, nodata_value, prism_data)[::-1]

        # Open the target terrain data to extract CRS and bounds
        with rasterio.open('Terrain.tif') as target_file:
            target_crs = target_file.crs
            target_bounds = target_file.bounds

        # Define the resolution of the new 4km grid (4000 meters)
        resolution = 4000  # 4 km resolution (in meters)

        # Calculate the dimensions of the new grid (height and width)
        width = int((target_bounds.right - target_bounds.left) / resolution)
        height = int((target_bounds.top - target_bounds.bottom) / resolution)

        # Define the transformation for the 4km grid
        transform = rasterio.transform.from_bounds(
            target_bounds.left, target_bounds.bottom, target_bounds.right, target_bounds.top, width, height
        )

        # Create an array to hold the reprojected PRISM data on the 4km grid
        prism_reprojected = np.full((height, width), nodata_value, dtype=np.float32)

        # Reproject the PRISM data to the 4km grid (nearest neighbor resampling)
        reproject(
            source=prism_data,
            destination=prism_reprojected,
            src_transform=prism_transform,
            src_crs=prism_crs,
            dst_transform=transform,
            dst_crs=target_crs,
            resampling=Resampling.cubic,  # Preserve original values
            src_nodata=nodata_value,
            dst_nodata=nodata_value
        )

        
        # Convert the current time value to a datetime object
        timestamp = pd.to_datetime(ds.time.values[i]).to_pydatetime()
        # Localize the timestamp to UTC, then convert to US/Eastern timezone
        # timestamp = pytz.utc.localize(timestamp).astimezone(pytz.timezone('US/Eastern'))
        
        # Convert back to numpy.datetime64 for xarray compatibility
        timestamp_np64 = np.datetime64(timestamp.isoformat())

        # Create an xarray Dataset and add dimensions
        nc_source_clip = xr.Dataset()

        # Create 1D arrays for the x and y coordinates on the 4km grid
        x_coords = np.linspace(target_bounds.left, target_bounds.right, width)
        y_coords = np.linspace(target_bounds.top, target_bounds.bottom, height)

        nc_source_clip['x'] = (('x',), x_coords.astype(np.float64))
        nc_source_clip['y'] = (('y',), y_coords.astype(np.float64))

        # Use datetime64 for the time variable
        nc_source_clip['time'] = (('time',),  np.array([timestamp_np64]))

        # Add the reprojected PRISM precipitation data to the xarray dataset
        nc_source_clip['precipitation'] = (('time', 'y', 'x'), np.expand_dims(prism_reprojected[::-1, :], axis=0))

        # Set attributes for precipitation variable
        nc_source_clip['precipitation'].attrs['units'] = "3 hr accumulation (mm)"
        nc_source_clip['precipitation'].attrs['missing_value'] = nodata_value
        nc_source_clip['precipitation'].attrs['esri_pe_string'] = target_crs.to_wkt()

        # Set global attributes
        nc_source_clip.attrs['Projection'] = target_crs.to_wkt()
        nc_source_clip.attrs['projection'] = target_crs.to_wkt()

        # Save the xarray Dataset to a NetCDF file
        nc_source_clip.to_netcdf(f'./Hourly_StageIV_data/NCEP_StageIV_data_precip2_{timestamp.strftime("%Y%m%d%H")}.nc', format='NETCDF4')
        print(np.array([np.datetime64(timestamp)]))

print("NetCDF files created with random precipitation data using xarray.")
