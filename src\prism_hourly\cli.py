"""
Command-line interface for the PRISM hourly dataset package.

This module provides a comprehensive CLI for all package functionality including
data download, processing, temporal disaggregation, and validation.
"""

import os
import sys
from pathlib import Path
from datetime import datetime, date
from typing import Optional, List

import click
from tqdm import tqdm

from .utils.config_manager import ConfigManager
from .utils.logging_utils import LoggingUtils
from .utils.validation_utils import ValidationUtils
from .data_download.prism_downloader import PRISMDownloader
from .data_download.ncep_downloader import NCEPDownloader
from .data_processing.data_processor import DataProcessor
from .data_processing.temporal_disaggregator import TemporalDisaggregator


@click.group()
@click.option('--config', '-c', type=click.Path(exists=True), 
              help='Path to configuration file')
@click.option('--log-level', type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR']),
              default='INFO', help='Logging level')
@click.option('--log-file', type=click.Path(), help='Path to log file')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose output')
@click.pass_context
def cli(ctx, config, log_level, log_file, verbose):
    """
    PRISM Hourly Dataset Creation Tool
    
    This tool provides functionality for downloading, processing, and creating
    hourly precipitation estimates from PRISM daily and NCEP Stage IV data.
    """
    # Ensure context object exists
    ctx.ensure_object(dict)
    
    # Configure logging
    if verbose:
        log_level = 'DEBUG'
    
    LoggingUtils.configure_logging(
        level=log_level,
        log_file=log_file,
        console_output=True
    )
    
    # Load configuration
    config_manager = ConfigManager(config)
    ctx.obj['config'] = config_manager.get_config()
    ctx.obj['config_manager'] = config_manager
    
    logger = LoggingUtils.get_logger(__name__)
    logger.info("PRISM Hourly CLI initialized")


@cli.group()
@click.pass_context
def download(ctx):
    """Download PRISM and NCEP data."""
    pass


@download.command()
@click.option('--start-date', type=click.DateTime(formats=['%Y-%m-%d']),
              required=True, help='Start date (YYYY-MM-DD)')
@click.option('--end-date', type=click.DateTime(formats=['%Y-%m-%d']),
              required=True, help='End date (YYYY-MM-DD)')
@click.option('--output-dir', type=click.Path(), 
              help='Output directory (overrides config)')
@click.option('--delay', type=float, help='Delay between downloads in seconds')
@click.option('--verify', is_flag=True, help='Verify downloads after completion')
@click.pass_context
def prism(ctx, start_date, end_date, output_dir, delay, verify):
    """Download PRISM daily precipitation data."""
    config = ctx.obj['config']
    logger = LoggingUtils.get_logger(__name__)
    
    # Override config with command line options
    download_config = config.download
    if output_dir:
        download_config.prism_output_dir = output_dir
    if delay:
        download_config.download_delay = delay
    
    logger.info(f"Downloading PRISM data from {start_date.date()} to {end_date.date()}")
    
    try:
        # Initialize downloader
        downloader = PRISMDownloader(
            output_dir=download_config.prism_output_dir,
            base_url=download_config.prism_base_url,
            delay_seconds=download_config.download_delay,
            max_retries=download_config.max_retries,
            timeout=download_config.timeout
        )
        
        # Download data
        downloaded_files = downloader.download_date_range(
            start_date.date(), end_date.date()
        )
        
        logger.info(f"Downloaded {len(downloaded_files)} files")
        
        # Verify downloads if requested
        if verify:
            logger.info("Verifying downloads...")
            verification_results = downloader.verify_downloads(downloaded_files)
            logger.info(f"Verification: {verification_results['valid']}/{verification_results['total']} files valid")
            
            if verification_results['invalid'] > 0:
                logger.warning(f"{verification_results['invalid']} invalid files found")
                for invalid_file in verification_results['invalid_files']:
                    logger.warning(f"Invalid: {invalid_file}")
        
        click.echo(f"Successfully downloaded {len(downloaded_files)} PRISM files")
        
    except Exception as e:
        logger.error(f"PRISM download failed: {e}")
        sys.exit(1)


@download.command()
@click.option('--start-year', type=int, required=True, help='Start year')
@click.option('--end-year', type=int, required=True, help='End year')
@click.option('--output-dir', type=click.Path(), 
              help='Output directory (overrides config)')
@click.option('--extract', is_flag=True, help='Extract downloaded tar files')
@click.pass_context
def ncep(ctx, start_year, end_year, output_dir, extract):
    """Download NCEP Stage IV radar data."""
    config = ctx.obj['config']
    logger = LoggingUtils.get_logger(__name__)
    
    # Override config with command line options
    download_config = config.download
    if output_dir:
        download_config.ncep_output_dir = output_dir
    
    logger.info(f"Downloading NCEP data for years {start_year}-{end_year}")
    
    try:
        # Initialize downloader
        downloader = NCEPDownloader(
            output_dir=download_config.ncep_output_dir,
            base_url=download_config.ncep_base_url,
            max_retries=download_config.max_retries,
            timeout=download_config.timeout
        )
        
        # Download data
        downloaded_files = downloader.download_year_range(start_year, end_year)
        
        logger.info(f"Downloaded {len(downloaded_files)} files")
        
        # Extract if requested
        if extract:
            logger.info("Extracting downloaded files...")
            extracted_files = downloader.extract_downloaded_files()
            logger.info(f"Extracted {len(extracted_files)} files")
        
        click.echo(f"Successfully downloaded {len(downloaded_files)} NCEP files")
        
    except Exception as e:
        logger.error(f"NCEP download failed: {e}")
        sys.exit(1)


@cli.group()
@click.pass_context
def process(ctx):
    """Process and reproject data."""
    pass


@process.command()
@click.option('--input-dir', type=click.Path(exists=True), required=True,
              help='Directory containing PRISM zip files')
@click.option('--output-dir', type=click.Path(), required=True,
              help='Output directory for processed files')
@click.option('--target-grid', type=click.Path(exists=True),
              help='Target grid file (overrides config)')
@click.pass_context
def prism(ctx, input_dir, output_dir, target_grid):
    """Process PRISM daily files to NetCDF format."""
    config = ctx.obj['config']
    logger = LoggingUtils.get_logger(__name__)
    
    # Override config with command line options
    processing_config = config.processing
    if target_grid:
        processing_config.target_grid_file = target_grid
    
    logger.info(f"Processing PRISM files from {input_dir} to {output_dir}")
    
    try:
        # Initialize processor
        processor = DataProcessor(
            target_grid_file=processing_config.target_grid_file,
            resolution=processing_config.resolution,
            nodata_value=processing_config.nodata_value,
            resampling_method=processing_config.resampling_method,
            compress_output=processing_config.compress_output
        )
        
        # Process files
        processed_files = processor.process_prism_files(
            input_dir, output_dir
        )
        
        logger.info(f"Processed {len(processed_files)} files")
        click.echo(f"Successfully processed {len(processed_files)} PRISM files")
        
    except Exception as e:
        logger.error(f"PRISM processing failed: {e}")
        sys.exit(1)


@cli.command()
@click.option('--prism-dir', type=click.Path(exists=True), required=True,
              help='Directory containing processed PRISM daily files')
@click.option('--ncep-dir', type=click.Path(exists=True), required=True,
              help='Directory containing processed NCEP hourly files')
@click.option('--output-dir', type=click.Path(), required=True,
              help='Output directory for hourly estimates')
@click.option('--hour-start', type=int, help='Start hour for disaggregation')
@click.option('--hour-end', type=int, help='End hour for disaggregation')
@click.option('--quality-control/--no-quality-control', default=True,
              help='Enable/disable quality control checks')
@click.pass_context
def disaggregate(ctx, prism_dir, ncep_dir, output_dir, hour_start, hour_end, quality_control):
    """Create hourly precipitation estimates using temporal disaggregation."""
    config = ctx.obj['config']
    logger = LoggingUtils.get_logger(__name__)
    
    # Override config with command line options
    disagg_config = config.disaggregation
    if hour_start is not None:
        disagg_config.hour_range_start = hour_start
    if hour_end is not None:
        disagg_config.hour_range_end = hour_end
    
    hour_range = (disagg_config.hour_range_start, disagg_config.hour_range_end)
    
    logger.info(f"Creating hourly estimates from {prism_dir} and {ncep_dir}")
    logger.info(f"Hour range: {hour_range[0]}Z to {hour_range[1]}Z")
    
    try:
        # Initialize disaggregator
        disaggregator = TemporalDisaggregator(
            prism_dir=prism_dir,
            ncep_dir=ncep_dir,
            output_dir=output_dir,
            hour_range=hour_range,
            quality_control=quality_control,
            mass_conservation_tolerance=disagg_config.tolerance,
            fill_missing_hours=disagg_config.fill_missing_hours
        )
        
        # Process all files
        stats = disaggregator.process_all_prism_files()
        
        logger.info(f"Disaggregation complete: {stats}")
        click.echo(f"Successfully created hourly estimates")
        click.echo(f"Processed: {stats['processed']}, Failed: {stats['failed']}, Skipped: {stats['skipped']}")
        
    except Exception as e:
        logger.error(f"Temporal disaggregation failed: {e}")
        sys.exit(1)


@cli.group()
@click.pass_context
def validate(ctx):
    """Validate data and check quality."""
    pass


@validate.command()
@click.argument('file_path', type=click.Path(exists=True))
@click.option('--spatial/--no-spatial', default=True, help='Check spatial consistency')
@click.option('--temporal/--no-temporal', default=True, help='Check temporal consistency')
@click.option('--ranges/--no-ranges', default=True, help='Check data ranges')
@click.option('--metadata/--no-metadata', default=True, help='Check metadata')
@click.pass_context
def dataset(ctx, file_path, spatial, temporal, ranges, metadata):
    """Validate a single dataset file."""
    logger = LoggingUtils.get_logger(__name__)
    
    logger.info(f"Validating dataset: {file_path}")
    
    try:
        validator = ValidationUtils()
        results = validator.validate_dataset(
            file_path,
            check_spatial=spatial,
            check_temporal=temporal,
            check_data_ranges=ranges,
            check_metadata=metadata
        )
        
        # Display results
        if results['is_valid']:
            click.echo(click.style("✓ Dataset is valid", fg='green'))
        else:
            click.echo(click.style("✗ Dataset validation failed", fg='red'))
        
        if results['errors']:
            click.echo("\nErrors:")
            for error in results['errors']:
                click.echo(click.style(f"  - {error}", fg='red'))
        
        if results['warnings']:
            click.echo("\nWarnings:")
            for warning in results['warnings']:
                click.echo(click.style(f"  - {warning}", fg='yellow'))
        
        # Display detailed results if verbose
        if ctx.parent.params.get('verbose'):
            click.echo(f"\nDetailed results: {results}")
        
    except Exception as e:
        logger.error(f"Validation failed: {e}")
        sys.exit(1)


@validate.command()
@click.option('--daily-file', type=click.Path(exists=True), required=True,
              help='Daily precipitation file')
@click.option('--hourly-dir', type=click.Path(exists=True), required=True,
              help='Directory containing hourly files')
@click.option('--tolerance', type=float, default=0.01,
              help='Mass conservation tolerance')
@click.pass_context
def conservation(ctx, daily_file, hourly_dir, tolerance):
    """Validate mass conservation between daily and hourly data."""
    logger = LoggingUtils.get_logger(__name__)
    
    logger.info(f"Checking mass conservation for {daily_file}")
    
    try:
        # Find corresponding hourly files
        daily_path = Path(daily_file)
        date_str = daily_path.stem.split('_')[1]  # Extract date from filename
        
        hourly_dir_path = Path(hourly_dir)
        hourly_files = list(hourly_dir_path.glob(f"*{date_str}*.nc"))
        
        if not hourly_files:
            click.echo(f"No hourly files found for date {date_str}")
            sys.exit(1)
        
        validator = ValidationUtils()
        results = validator.validate_mass_conservation(
            daily_file, [str(f) for f in hourly_files], tolerance
        )
        
        # Display results
        if results['is_valid']:
            click.echo(click.style("✓ Mass conservation satisfied", fg='green'))
        else:
            click.echo(click.style("✗ Mass conservation violated", fg='red'))
        
        if 'conservation_stats' in results:
            stats = results['conservation_stats']
            click.echo(f"Max relative error: {stats['max_relative_error']:.4f}")
            click.echo(f"Mean relative error: {stats['mean_relative_error']:.4f}")
            click.echo(f"Tolerance: {stats['tolerance']:.4f}")
        
        if results['errors']:
            for error in results['errors']:
                click.echo(click.style(f"Error: {error}", fg='red'))
        
    except Exception as e:
        logger.error(f"Mass conservation check failed: {e}")
        sys.exit(1)


@cli.command()
@click.option('--config-file', type=click.Path(), 
              help='Configuration file to validate')
@click.pass_context
def config(ctx, config_file):
    """Display or validate configuration."""
    if config_file:
        config_manager = ConfigManager(config_file)
    else:
        config_manager = ctx.obj['config_manager']
    
    # Validate configuration
    is_valid = config_manager.validate_config()
    
    if is_valid:
        click.echo(click.style("✓ Configuration is valid", fg='green'))
    else:
        click.echo(click.style("✗ Configuration has errors", fg='red'))
    
    # Display configuration
    config_manager.print_config()


if __name__ == '__main__':
    cli()
