import rasterio
from rasterio.warp import calculate_default_transform, reproject, Resampling
import numpy as np
import os
from datetime import datetime, timedelta
import pytz
import xarray as xr
import glob

# Get the list of files
_paths_prism_daily = np.sort(glob.glob('/lcrc/project/Hydro-model/PRISM/daily/*/*_bil.zip'))

# Filter the paths by excluding files with years 1981-2001 and 2022-2023
filtered_paths = [
    path for path in _paths_prism_daily
    if not any(str(year) in path for year in range(1981, 2022))
]

# Modify each path by appending the part of the filename before ".zip"
modified_paths = [
    os.path.join(path, os.path.basename(path).replace('.zip', '') + '.bil')
    for path in filtered_paths
]

source_precip_files = modified_paths
nodata_value = -9999

for i, file in enumerate(source_precip_files):
    # Open the PRISM file
    with rasterio.open("/vsizip/"+file) as prism_file:

        prism_data = prism_file.read(1)  # Read the first band
        prism_crs = prism_file.crs
        prism_transform = prism_file.transform
        prism_data = np.where(prism_data == nodata_value, np.nan, prism_data)  # Mask nodata values

        # Open the target terrain data to extract CRS and bounds
        with rasterio.open('Terrain.tif') as target_file:
            target_crs = target_file.crs
            target_bounds = target_file.bounds

        # Define the resolution of the new 4km grid (4000 meters)
        resolution = 4300  # 4 km

        # Calculate the dimensions of the new grid based on the bounds and 4km resolution
        width = int((target_bounds.right - target_bounds.left) / resolution)
        height = int((target_bounds.top - target_bounds.bottom) / resolution)

        # Define the transformation for the 4km grid with the same CRS as the target
        transform = rasterio.transform.from_bounds(
            target_bounds.left, target_bounds.bottom, target_bounds.right, target_bounds.top, width, height
        )

        # Create an array to hold the reprojected PRISM data on the 4km grid
        prism_reprojected = np.full((height, width), np.nan, dtype=prism_data.dtype)

        # Reproject the PRISM data to the 4km grid (nearest neighbor resampling)
        reproject(
            source=prism_data,
            destination=prism_reprojected,
            src_transform=prism_transform,
            src_crs=prism_crs,
            dst_transform=transform,
            dst_crs=target_crs,
            resampling=Resampling.nearest,  # Use nearest if no interpolation is desired
            src_nodata=np.nan,
            dst_nodata=np.nan
        )

    # Adjust the timestamp generation to detect the date in the .bil filename
    file_name = os.path.basename(file)
    date_str = file_name.split('_')[4]  # Extract date from filename
    year = int(date_str[0:4])
    month = int(date_str[4:6])
    day = int(date_str[6:])
    hour = 1  # Assuming hour is constant; adjust if needed

    timestamp = datetime(year, month, day, 0, 0, 0)
    timestamp = pytz.timezone('UTC').localize(timestamp).astimezone(pytz.timezone('US/Eastern'))

    # Create an xarray Dataset and add dimensions
    nc_source_clip = xr.Dataset()

    # Create 1D arrays for the x and y coordinates on the 4km grid
    x_coords = np.linspace(target_bounds.left, target_bounds.right, width)
    y_coords = np.linspace(target_bounds.top, target_bounds.bottom, height)

    nc_source_clip['x'] = (('x',), x_coords.astype(np.float64))
    nc_source_clip['y'] = (('y',), np.flipud(y_coords).astype(np.float64))

    # Use datetime64 for time variable
    nc_source_clip['time'] = (('time',), np.array([np.datetime64(timestamp)]))

    # Add the reprojected PRISM precipitation data with random values to the xarray dataset
    nc_source_clip['precipitation'] = (('time', 'y', 'x'), np.expand_dims(prism_reprojected[::-1, :], axis=0))

    # Set attributes for precipitation variable
    nc_source_clip['precipitation'].attrs['units'] = "3 hr accumulation (mm)"
    nc_source_clip['precipitation'].attrs['missing_value'] = -9999.0
    nc_source_clip['precipitation'].attrs['esri_pe_string'] = target_crs.to_wkt()

    # Set global attributes
    nc_source_clip.attrs['Projection'] = target_crs.to_wkt()
    nc_source_clip.attrs['projection'] = target_crs.to_wkt()

    # Save the xarray Dataset to a NetCDF file
    nc_source_clip.to_netcdf(f'/lcrc/project/Hydro-model/output_Prism_daily/PRISM_{timestamp.strftime("%Y%m%d")}.nc', format='NETCDF4')
    print(np.array([np.datetime64(timestamp)]))

print("NetCDF file created with random precipitation data using xarray.")
