"""
Logging utilities module.

This module provides centralized logging configuration and utilities for the
PRISM hourly dataset package.
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any
import time
from datetime import datetime


class LoggingUtils:
    """
    Centralized logging utilities for the PRISM hourly package.
    
    This class provides a consistent logging interface across all modules,
    with support for file and console output, log rotation, and different
    log levels for different components.
    
    Attributes:
        _loggers (Dict[str, logging.Logger]): Cache of configured loggers
        _configured (bool): Whether logging has been configured
    
    Example:
        >>> LoggingUtils.configure_logging(level="INFO", log_file="./logs/app.log")
        >>> logger = LoggingUtils.get_logger(__name__)
        >>> logger.info("Application started")
    """
    
    _loggers: Dict[str, logging.Logger] = {}
    _configured: bool = False
    _log_file: Optional[str] = None
    _log_level: str = "INFO"
    _console_output: bool = True
    
    @classmethod
    def configure_logging(
        cls,
        level: str = "INFO",
        log_file: Optional[str] = None,
        max_file_size: str = "10MB",
        backup_count: int = 5,
        format_string: Optional[str] = None,
        console_output: bool = True,
        force_reconfigure: bool = False
    ):
        """
        Configure logging for the entire package.
        
        Args:
            level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            log_file: Path to log file. If None, only console logging is used
            max_file_size: Maximum size of log file before rotation
            backup_count: Number of backup log files to keep
            format_string: Custom log format string
            console_output: Whether to output logs to console
            force_reconfigure: Force reconfiguration even if already configured
        """
        if cls._configured and not force_reconfigure:
            return
        
        # Store configuration
        cls._log_level = level.upper()
        cls._log_file = log_file
        cls._console_output = console_output
        
        # Clear existing loggers
        if force_reconfigure:
            cls._loggers.clear()
        
        # Set up root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, cls._log_level))
        
        # Clear existing handlers
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # Create formatter
        if format_string is None:
            format_string = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        
        formatter = logging.Formatter(format_string)
        
        # Add console handler
        if console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(getattr(logging, cls._log_level))
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
        
        # Add file handler if specified
        if log_file:
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Parse file size
            size_bytes = cls._parse_size(max_file_size)
            
            # Create rotating file handler
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=size_bytes,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setLevel(getattr(logging, cls._log_level))
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
        
        cls._configured = True
        
        # Log configuration
        logger = cls.get_logger(__name__)
        logger.info(f"Logging configured - Level: {cls._log_level}, File: {log_file}")
    
    @classmethod
    def get_logger(cls, name: str) -> logging.Logger:
        """
        Get a logger instance for the specified name.
        
        Args:
            name: Logger name (typically __name__ of the calling module)
            
        Returns:
            Configured logger instance
        """
        # Configure with defaults if not already configured
        if not cls._configured:
            cls.configure_logging()
        
        # Return cached logger if available
        if name in cls._loggers:
            return cls._loggers[name]
        
        # Create new logger
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, cls._log_level))
        
        # Cache and return
        cls._loggers[name] = logger
        return logger
    
    @classmethod
    def set_level(cls, level: str, logger_name: Optional[str] = None):
        """
        Set logging level for a specific logger or all loggers.
        
        Args:
            level: New logging level
            logger_name: Specific logger name, or None for all loggers
        """
        level = level.upper()
        
        if logger_name:
            if logger_name in cls._loggers:
                cls._loggers[logger_name].setLevel(getattr(logging, level))
        else:
            # Update all loggers
            cls._log_level = level
            logging.getLogger().setLevel(getattr(logging, level))
            
            for logger in cls._loggers.values():
                logger.setLevel(getattr(logging, level))
    
    @classmethod
    def add_file_handler(
        cls,
        log_file: str,
        level: Optional[str] = None,
        format_string: Optional[str] = None,
        max_file_size: str = "10MB",
        backup_count: int = 5
    ):
        """
        Add an additional file handler to the root logger.
        
        Args:
            log_file: Path to additional log file
            level: Logging level for this handler
            format_string: Custom format for this handler
            max_file_size: Maximum file size before rotation
            backup_count: Number of backup files to keep
        """
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create formatter
        if format_string is None:
            format_string = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        formatter = logging.Formatter(format_string)
        
        # Parse file size
        size_bytes = cls._parse_size(max_file_size)
        
        # Create handler
        handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=size_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        
        handler.setLevel(getattr(logging, level or cls._log_level))
        handler.setFormatter(formatter)
        
        # Add to root logger
        logging.getLogger().addHandler(handler)
    
    @classmethod
    def create_progress_logger(cls, name: str, log_file: str) -> logging.Logger:
        """
        Create a specialized logger for progress tracking.
        
        Args:
            name: Logger name
            log_file: Path to progress log file
            
        Returns:
            Progress logger instance
        """
        logger = logging.getLogger(f"{name}.progress")
        logger.setLevel(logging.INFO)
        
        # Remove existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # Create file handler
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
        formatter = logging.Formatter("%(asctime)s - %(message)s")
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        # Prevent propagation to root logger
        logger.propagate = False
        
        return logger
    
    @classmethod
    def log_function_call(cls, func):
        """
        Decorator to log function calls with timing information.
        
        Args:
            func: Function to decorate
            
        Returns:
            Decorated function
        """
        def wrapper(*args, **kwargs):
            logger = cls.get_logger(func.__module__)
            start_time = time.time()
            
            # Log function entry
            logger.debug(f"Entering {func.__name__}")
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.debug(f"Completed {func.__name__} in {execution_time:.2f}s")
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"Error in {func.__name__} after {execution_time:.2f}s: {e}")
                raise
        
        return wrapper
    
    @classmethod
    def log_performance(cls, logger: logging.Logger, operation: str, start_time: float):
        """
        Log performance information for an operation.
        
        Args:
            logger: Logger instance to use
            operation: Description of the operation
            start_time: Start time of the operation
        """
        execution_time = time.time() - start_time
        logger.info(f"{operation} completed in {execution_time:.2f}s")
    
    @classmethod
    def _parse_size(cls, size_str: str) -> int:
        """
        Parse a size string (e.g., '10MB', '1GB') to bytes.
        
        Args:
            size_str: Size string to parse
            
        Returns:
            Size in bytes
        """
        size_str = size_str.upper().strip()
        
        # Extract number and unit
        import re
        match = re.match(r'^(\d+(?:\.\d+)?)\s*([KMGT]?B?)$', size_str)
        
        if not match:
            raise ValueError(f"Invalid size format: {size_str}")
        
        number = float(match.group(1))
        unit = match.group(2) or 'B'
        
        # Convert to bytes
        multipliers = {
            'B': 1,
            'KB': 1024,
            'MB': 1024 ** 2,
            'GB': 1024 ** 3,
            'TB': 1024 ** 4
        }
        
        return int(number * multipliers[unit])
    
    @classmethod
    def create_timestamped_logger(cls, base_name: str, log_dir: str) -> logging.Logger:
        """
        Create a logger with a timestamped log file.
        
        Args:
            base_name: Base name for the logger
            log_dir: Directory to store log files
            
        Returns:
            Logger with timestamped file handler
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = Path(log_dir) / f"{base_name}_{timestamp}.log"
        
        logger = logging.getLogger(f"{base_name}_{timestamp}")
        logger.setLevel(getattr(logging, cls._log_level))
        
        # Create file handler
        log_file.parent.mkdir(parents=True, exist_ok=True)
        handler = logging.FileHandler(log_file, encoding='utf-8')
        
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        return logger
    
    @classmethod
    def get_log_summary(cls) -> Dict[str, Any]:
        """
        Get a summary of current logging configuration.
        
        Returns:
            Dictionary with logging configuration summary
        """
        return {
            'configured': cls._configured,
            'level': cls._log_level,
            'log_file': cls._log_file,
            'console_output': cls._console_output,
            'active_loggers': len(cls._loggers),
            'logger_names': list(cls._loggers.keys())
        }
