[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "prism-hourly-dataset"
version = "1.0.0"
description = "Create hourly precipitation datasets by combining daily PRISM data with NCEP Stage IV radar data"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "PRISM Hourly Dataset Contributors", email = "<EMAIL>"}
]
maintainers = [
    {name = "PRISM Hourly Dataset Contributors", email = "<EMAIL>"}
]
keywords = ["precipitation", "PRISM", "NCEP", "temporal disaggregation", "meteorology", "climate"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Atmospheric Science",
    "Topic :: Scientific/Engineering :: GIS",
]
requires-python = ">=3.8"
dependencies = [
    "click>=8.0.0",
    "numpy>=1.20.0",
    "pandas>=1.3.0",
    "xarray>=0.19.0",
    "netcdf4>=1.5.0",
    "rasterio>=1.2.0",
    "requests>=2.25.0",
    "pyyaml>=5.4.0",
    "scipy>=1.7.0",
    "tqdm>=4.60.0",
    "pyproj>=3.0.0",
    "matplotlib>=3.3.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0.0",
    "pytest-cov>=2.12.0",
    "black>=21.0.0",
    "isort>=5.9.0",
    "flake8>=3.9.0",
    "mypy>=0.910",
    "pre-commit>=2.15.0",
    "sphinx>=4.0.0",
    "sphinx-rtd-theme>=1.0.0",
    "jupyter>=1.0.0",
    "ipykernel>=6.0.0",
]

[project.urls]
Homepage = "https://github.com/your-org/prism-hourly-dataset"
Documentation = "https://prism-hourly-dataset.readthedocs.io/"
Repository = "https://github.com/your-org/prism-hourly-dataset.git"
"Bug Tracker" = "https://github.com/your-org/prism-hourly-dataset/issues"
Changelog = "https://github.com/your-org/prism-hourly-dataset/blob/main/CHANGELOG.md"

[project.scripts]
prism-hourly = "prism_hourly.cli:cli"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
prism_hourly = ["config/*", "data/*"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["prism_hourly"]

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.coverage.run]
source = ["src/prism_hourly"]
omit = ["*/tests/*", "*/test_*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "rasterio.*",
    "netCDF4.*",
    "pyproj.*",
    "matplotlib.*",
]
ignore_missing_imports = true
