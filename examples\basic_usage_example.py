#!/usr/bin/env python3
"""
Basic usage example for the PRISM Hourly Dataset package.

This script demonstrates the basic workflow for creating hourly precipitation
estimates from PRISM daily and NCEP Stage IV data.
"""

import os
import sys
from pathlib import Path
from datetime import date, datetime
import logging

# Add the package to the path for development
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from prism_hourly.utils.config_manager import ConfigManager
from prism_hourly.utils.logging_utils import LoggingUtils
from prism_hourly.data_download.prism_downloader import PRISMDownloader
from prism_hourly.data_download.ncep_downloader import NCEPDownloader
from prism_hourly.data_processing.data_processor import DataProcessor
from prism_hourly.data_processing.temporal_disaggregator import TemporalDisaggregator
from prism_hourly.utils.validation_utils import ValidationUtils


def main():
    """Main function demonstrating basic package usage."""
    
    # Configure logging
    LoggingUtils.configure_logging(
        level='INFO',
        console_output=True,
        log_file='basic_usage_example.log'
    )
    logger = LoggingUtils.get_logger(__name__)
    
    logger.info("Starting PRISM Hourly Dataset basic usage example")
    
    # Load configuration
    config_file = Path(__file__).parent.parent / "config" / "config.yaml"
    config_manager = ConfigManager(str(config_file))
    config = config_manager.get_config()
    
    logger.info("Configuration loaded successfully")
    
    # Create output directories
    data_dir = Path("./example_data")
    prism_raw_dir = data_dir / "prism_raw"
    ncep_raw_dir = data_dir / "ncep_raw"
    prism_processed_dir = data_dir / "prism_processed"
    ncep_processed_dir = data_dir / "ncep_processed"
    hourly_output_dir = data_dir / "prism_hourly"
    
    for directory in [prism_raw_dir, ncep_raw_dir, prism_processed_dir, 
                     ncep_processed_dir, hourly_output_dir]:
        directory.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"Created output directories under {data_dir}")
    
    # Example 1: Download PRISM data for a few days
    logger.info("Example 1: Downloading PRISM data")
    
    try:
        prism_downloader = PRISMDownloader(
            output_dir=str(prism_raw_dir),
            delay_seconds=1.0,  # Reduced delay for example
            max_retries=2
        )
        
        # Download data for a small date range
        start_date = date(2023, 7, 1)
        end_date = date(2023, 7, 3)
        
        logger.info(f"Downloading PRISM data from {start_date} to {end_date}")
        downloaded_files = prism_downloader.download_date_range(start_date, end_date)
        
        logger.info(f"Downloaded {len(downloaded_files)} PRISM files")
        
        # Verify downloads
        verification_results = prism_downloader.verify_downloads(downloaded_files)
        logger.info(f"Verification: {verification_results['valid']}/{verification_results['total']} files valid")
        
    except Exception as e:
        logger.error(f"PRISM download failed: {e}")
        logger.info("Continuing with example using mock data...")
    
    # Example 2: Download NCEP data (commented out due to large file sizes)
    logger.info("Example 2: NCEP data download (skipped in example)")
    logger.info("NCEP downloads are large files - see documentation for full usage")
    
    # Example 3: Process PRISM data
    logger.info("Example 3: Processing PRISM data")
    
    try:
        # Check if we have any PRISM files to process
        prism_files = list(prism_raw_dir.glob("*.zip"))
        
        if prism_files:
            processor = DataProcessor(
                resolution=config.processing.resolution,
                nodata_value=config.processing.nodata_value,
                resampling_method=config.processing.resampling_method
            )
            
            logger.info(f"Processing {len(prism_files)} PRISM files")
            processed_files = processor.process_prism_files(
                str(prism_raw_dir),
                str(prism_processed_dir)
            )
            
            logger.info(f"Processed {len(processed_files)} files")
        else:
            logger.info("No PRISM files found to process")
            
    except Exception as e:
        logger.error(f"PRISM processing failed: {e}")
    
    # Example 4: Temporal disaggregation (requires both PRISM and NCEP data)
    logger.info("Example 4: Temporal disaggregation")
    
    try:
        # Check if we have processed data
        prism_processed_files = list(prism_processed_dir.glob("*.nc"))
        ncep_processed_files = list(ncep_processed_dir.glob("*.nc"))
        
        if prism_processed_files and ncep_processed_files:
            disaggregator = TemporalDisaggregator(
                prism_dir=str(prism_processed_dir),
                ncep_dir=str(ncep_processed_dir),
                output_dir=str(hourly_output_dir),
                hour_range=(config.disaggregation.hour_range_start, 
                           config.disaggregation.hour_range_end),
                quality_control=True
            )
            
            logger.info("Starting temporal disaggregation")
            stats = disaggregator.process_all_prism_files()
            
            logger.info(f"Disaggregation complete: {stats}")
        else:
            logger.info("Insufficient data for temporal disaggregation")
            logger.info("Need both processed PRISM and NCEP data")
            
    except Exception as e:
        logger.error(f"Temporal disaggregation failed: {e}")
    
    # Example 5: Validation
    logger.info("Example 5: Data validation")
    
    try:
        validator = ValidationUtils()
        
        # Validate any processed files
        all_output_files = (
            list(prism_processed_dir.glob("*.nc")) + 
            list(hourly_output_dir.glob("*.nc"))
        )
        
        if all_output_files:
            for file_path in all_output_files[:3]:  # Validate first 3 files
                logger.info(f"Validating {file_path.name}")
                
                results = validator.validate_dataset(str(file_path))
                
                if results['is_valid']:
                    logger.info(f"✓ {file_path.name} is valid")
                else:
                    logger.warning(f"✗ {file_path.name} has validation issues")
                    for error in results['errors']:
                        logger.warning(f"  Error: {error}")
        else:
            logger.info("No output files found to validate")
            
    except Exception as e:
        logger.error(f"Validation failed: {e}")
    
    # Example 6: Configuration management
    logger.info("Example 6: Configuration management")
    
    try:
        # Display current configuration
        logger.info("Current configuration:")
        config_manager.print_config()
        
        # Validate configuration
        is_valid = config_manager.validate_config()
        logger.info(f"Configuration is valid: {is_valid}")
        
        # Example of accessing specific config values
        logger.info(f"PRISM base URL: {config.download.prism_base_url}")
        logger.info(f"Target resolution: {config.processing.resolution}m")
        logger.info(f"Hour range: {config.disaggregation.hour_range_start}-{config.disaggregation.hour_range_end}")
        
    except Exception as e:
        logger.error(f"Configuration management failed: {e}")
    
    logger.info("Basic usage example completed")
    logger.info(f"Check the log file 'basic_usage_example.log' for detailed output")
    logger.info(f"Output data saved to: {data_dir}")


if __name__ == "__main__":
    main()
