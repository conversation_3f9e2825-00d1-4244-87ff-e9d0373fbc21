#!/bin/bash

# CLI Usage Examples for PRISM Hourly Dataset Package
# 
# This script demonstrates various command-line interface usage patterns
# for the PRISM Hourly Dataset creation package.
#
# Note: This script is for demonstration purposes. 
# Adjust paths and dates according to your needs.

echo "=== PRISM Hourly Dataset CLI Examples ==="
echo ""

# Set up variables
START_DATE="2023-07-01"
END_DATE="2023-07-31"
START_YEAR=2023
END_YEAR=2023
DATA_DIR="./cli_example_data"
CONFIG_FILE="./config/config.yaml"

echo "Configuration:"
echo "  Start Date: $START_DATE"
echo "  End Date: $END_DATE"
echo "  Data Directory: $DATA_DIR"
echo "  Config File: $CONFIG_FILE"
echo ""

# Create data directories
echo "Creating data directories..."
mkdir -p $DATA_DIR/prism_raw
mkdir -p $DATA_DIR/ncep_raw
mkdir -p $DATA_DIR/prism_processed
mkdir -p $DATA_DIR/ncep_processed
mkdir -p $DATA_DIR/prism_hourly
echo "✓ Directories created"
echo ""

# Example 1: Display help
echo "=== Example 1: Display Help ==="
echo "Command: prism-hourly --help"
echo ""
prism-hourly --help
echo ""

# Example 2: View configuration
echo "=== Example 2: View Configuration ==="
echo "Command: prism-hourly config"
echo ""
prism-hourly config
echo ""

# Example 3: Download PRISM data
echo "=== Example 3: Download PRISM Data ==="
echo "Command: prism-hourly download prism --start-date $START_DATE --end-date $END_DATE --output-dir $DATA_DIR/prism_raw --verify"
echo ""
echo "Note: This will download actual data from PRISM servers"
echo "Uncomment the line below to run the download:"
echo ""
# prism-hourly download prism --start-date $START_DATE --end-date $END_DATE --output-dir $DATA_DIR/prism_raw --verify
echo "# Download skipped in example"
echo ""

# Example 4: Download NCEP data
echo "=== Example 4: Download NCEP Data ==="
echo "Command: prism-hourly download ncep --start-year $START_YEAR --end-year $END_YEAR --output-dir $DATA_DIR/ncep_raw --extract"
echo ""
echo "Note: NCEP files are very large (several GB per month)"
echo "Uncomment the line below to run the download:"
echo ""
# prism-hourly download ncep --start-year $START_YEAR --end-year $END_YEAR --output-dir $DATA_DIR/ncep_raw --extract
echo "# Download skipped in example"
echo ""

# Example 5: Process PRISM data
echo "=== Example 5: Process PRISM Data ==="
echo "Command: prism-hourly process prism --input-dir $DATA_DIR/prism_raw --output-dir $DATA_DIR/prism_processed"
echo ""
if [ "$(ls -A $DATA_DIR/prism_raw 2>/dev/null)" ]; then
    prism-hourly process prism --input-dir $DATA_DIR/prism_raw --output-dir $DATA_DIR/prism_processed
else
    echo "No PRISM files found in $DATA_DIR/prism_raw"
    echo "Run the download command first to get data"
fi
echo ""

# Example 6: Temporal disaggregation
echo "=== Example 6: Temporal Disaggregation ==="
echo "Command: prism-hourly disaggregate --prism-dir $DATA_DIR/prism_processed --ncep-dir $DATA_DIR/ncep_processed --output-dir $DATA_DIR/prism_hourly --hour-start 12 --hour-end 36"
echo ""
if [ "$(ls -A $DATA_DIR/prism_processed 2>/dev/null)" ] && [ "$(ls -A $DATA_DIR/ncep_processed 2>/dev/null)" ]; then
    prism-hourly disaggregate \
        --prism-dir $DATA_DIR/prism_processed \
        --ncep-dir $DATA_DIR/ncep_processed \
        --output-dir $DATA_DIR/prism_hourly \
        --hour-start 12 \
        --hour-end 36
else
    echo "Insufficient data for disaggregation"
    echo "Need both processed PRISM and NCEP data"
fi
echo ""

# Example 7: Validate a dataset
echo "=== Example 7: Validate Dataset ==="
echo "Command: prism-hourly validate dataset <file_path> --spatial --temporal --ranges --metadata"
echo ""
# Find a sample file to validate
SAMPLE_FILE=$(find $DATA_DIR -name "*.nc" | head -1)
if [ -n "$SAMPLE_FILE" ]; then
    echo "Validating: $SAMPLE_FILE"
    prism-hourly validate dataset "$SAMPLE_FILE" --spatial --temporal --ranges --metadata
else
    echo "No NetCDF files found to validate"
    echo "Process some data first to create files for validation"
fi
echo ""

# Example 8: Check mass conservation
echo "=== Example 8: Check Mass Conservation ==="
echo "Command: prism-hourly validate conservation --daily-file <daily_file> --hourly-dir <hourly_dir> --tolerance 0.01"
echo ""
DAILY_FILE=$(find $DATA_DIR/prism_processed -name "*.nc" | head -1)
if [ -n "$DAILY_FILE" ] && [ "$(ls -A $DATA_DIR/prism_hourly 2>/dev/null)" ]; then
    echo "Checking mass conservation for: $DAILY_FILE"
    prism-hourly validate conservation \
        --daily-file "$DAILY_FILE" \
        --hourly-dir $DATA_DIR/prism_hourly \
        --tolerance 0.01
else
    echo "Need both daily and hourly files for mass conservation check"
fi
echo ""

# Example 9: Verbose logging
echo "=== Example 9: Verbose Logging ==="
echo "Command: prism-hourly --verbose --log-level DEBUG config"
echo ""
prism-hourly --verbose --log-level DEBUG config
echo ""

# Example 10: Custom configuration file
echo "=== Example 10: Custom Configuration ==="
echo "Command: prism-hourly --config $CONFIG_FILE config"
echo ""
if [ -f "$CONFIG_FILE" ]; then
    prism-hourly --config $CONFIG_FILE config
else
    echo "Configuration file not found: $CONFIG_FILE"
    echo "Using default configuration"
    prism-hourly config
fi
echo ""

# Example 11: Download with custom settings
echo "=== Example 11: Download with Custom Settings ==="
echo "Command: prism-hourly download prism --start-date $START_DATE --end-date $START_DATE --delay 5.0 --output-dir $DATA_DIR/prism_custom"
echo ""
echo "This example shows custom download settings:"
echo "  - Single day download"
echo "  - Custom delay between requests"
echo "  - Custom output directory"
echo ""
mkdir -p $DATA_DIR/prism_custom
echo "# Uncomment to run:"
echo "# prism-hourly download prism --start-date $START_DATE --end-date $START_DATE --delay 5.0 --output-dir $DATA_DIR/prism_custom"
echo ""

# Example 12: Processing with custom grid
echo "=== Example 12: Processing with Custom Grid ==="
echo "Command: prism-hourly process prism --input-dir $DATA_DIR/prism_raw --output-dir $DATA_DIR/prism_custom_grid --target-grid ./config/custom_grid.txt"
echo ""
echo "This example shows processing with a custom target grid"
echo "Note: Requires a custom grid definition file"
echo ""
mkdir -p $DATA_DIR/prism_custom_grid
echo "# Uncomment to run (requires custom grid file):"
echo "# prism-hourly process prism --input-dir $DATA_DIR/prism_raw --output-dir $DATA_DIR/prism_custom_grid --target-grid ./config/custom_grid.txt"
echo ""

# Example 13: Disaggregation without quality control
echo "=== Example 13: Disaggregation without Quality Control ==="
echo "Command: prism-hourly disaggregate --prism-dir $DATA_DIR/prism_processed --ncep-dir $DATA_DIR/ncep_processed --output-dir $DATA_DIR/prism_hourly_fast --no-quality-control"
echo ""
echo "This example shows faster processing by disabling quality control"
echo "Note: Only recommended for trusted data"
echo ""
mkdir -p $DATA_DIR/prism_hourly_fast
echo "# Uncomment to run:"
echo "# prism-hourly disaggregate --prism-dir $DATA_DIR/prism_processed --ncep-dir $DATA_DIR/ncep_processed --output-dir $DATA_DIR/prism_hourly_fast --no-quality-control"
echo ""

# Example 14: Batch processing script
echo "=== Example 14: Batch Processing Script ==="
echo "Creating a batch processing script..."
echo ""

cat > $DATA_DIR/batch_process.sh << 'EOF'
#!/bin/bash
# Batch processing script for multiple years

YEARS=(2020 2021 2022 2023)
BASE_DIR="./batch_data"

for YEAR in "${YEARS[@]}"; do
    echo "Processing year: $YEAR"
    
    # Create year-specific directories
    mkdir -p $BASE_DIR/$YEAR/{prism_raw,ncep_raw,prism_processed,prism_hourly}
    
    # Download data
    prism-hourly download prism \
        --start-date $YEAR-01-01 \
        --end-date $YEAR-12-31 \
        --output-dir $BASE_DIR/$YEAR/prism_raw
    
    prism-hourly download ncep \
        --start-year $YEAR \
        --end-year $YEAR \
        --output-dir $BASE_DIR/$YEAR/ncep_raw \
        --extract
    
    # Process data
    prism-hourly process prism \
        --input-dir $BASE_DIR/$YEAR/prism_raw \
        --output-dir $BASE_DIR/$YEAR/prism_processed
    
    # Create hourly estimates
    prism-hourly disaggregate \
        --prism-dir $BASE_DIR/$YEAR/prism_processed \
        --ncep-dir $BASE_DIR/$YEAR/ncep_raw \
        --output-dir $BASE_DIR/$YEAR/prism_hourly
    
    echo "Completed year: $YEAR"
done

echo "Batch processing completed for all years"
EOF

chmod +x $DATA_DIR/batch_process.sh
echo "✓ Batch processing script created: $DATA_DIR/batch_process.sh"
echo ""

# Summary
echo "=== Summary ==="
echo ""
echo "This script demonstrated the following CLI capabilities:"
echo "1. ✓ Help and configuration display"
echo "2. ✓ Data download (PRISM and NCEP)"
echo "3. ✓ Data processing and format conversion"
echo "4. ✓ Temporal disaggregation"
echo "5. ✓ Data validation and quality control"
echo "6. ✓ Mass conservation checking"
echo "7. ✓ Custom configuration and logging"
echo "8. ✓ Batch processing workflows"
echo ""
echo "Key CLI commands:"
echo "  prism-hourly download prism    # Download PRISM daily data"
echo "  prism-hourly download ncep     # Download NCEP Stage IV data"
echo "  prism-hourly process prism     # Process PRISM data"
echo "  prism-hourly disaggregate      # Create hourly estimates"
echo "  prism-hourly validate dataset  # Validate data files"
echo "  prism-hourly validate conservation  # Check mass conservation"
echo "  prism-hourly config            # View/validate configuration"
echo ""
echo "For more information:"
echo "  prism-hourly --help"
echo "  prism-hourly <command> --help"
echo ""
echo "Output data saved to: $DATA_DIR"
echo "Batch processing script: $DATA_DIR/batch_process.sh"
