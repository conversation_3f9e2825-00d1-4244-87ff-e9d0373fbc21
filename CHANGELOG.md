# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-01-XX

### Added
- Initial release of PRISM Hourly Dataset Creator
- Automated PRISM daily precipitation data download
- Automated NCEP Stage IV radar data download
- Data processing and grid reprojection capabilities
- Temporal disaggregation algorithm for creating hourly estimates
- Command-line interface for all operations
- Configuration management system
- Comprehensive validation and quality control
- Example scripts and Jupyter notebooks
- Full test suite
- Documentation and API reference

### Features
- **Data Download**: Support for PRISM and NCEP data sources
- **Spatial Processing**: Automatic reprojection to 4km CONUS grid
- **Temporal Disaggregation**: Mass-conserving hourly estimation
- **Quality Control**: Comprehensive validation checks
- **CLI Interface**: Easy-to-use command-line tools
- **Python API**: Programmatic access to all functionality
- **Configuration**: Flexible YAML-based configuration system

### Technical Details
- Python 3.8+ support
- Modern packaging with pyproject.toml
- Type hints and comprehensive documentation
- Extensive test coverage
- CI/CD ready with pre-commit hooks
