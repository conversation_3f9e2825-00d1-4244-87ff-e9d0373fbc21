# PRISM Hourly Dataset Configuration File
# This file contains default configuration settings for the package

# Data download configuration
download:
  # PRISM data settings
  prism_base_url: "https://data.prism.oregonstate.edu/daily/ppt"
  prism_output_dir: "./data/prism_raw"
  
  # NCEP Stage IV data settings
  ncep_base_url: "https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4"
  ncep_output_dir: "./data/ncep_raw"
  
  # Download behavior
  download_delay: 2.0  # seconds between downloads
  max_retries: 3
  timeout: 300  # seconds
  verify_downloads: true

# Data processing configuration
processing:
  # Grid configuration
  target_grid_file: "./config/Terrain.tif"
  grid_definition_file: "./config/grid_4kmCONUS.txt"
  resolution: 4000.0  # meters
  
  # Resampling settings
  resampling_method: "cubic"  # options: nearest, bilinear, cubic, cubic_spline
  
  # Output directories
  prism_processed_dir: "./data/prism_processed"
  ncep_processed_dir: "./data/ncep_processed"
  
  # Processing options
  nodata_value: -9999.0
  compress_output: true
  chunk_size: 1000  # for memory management

# Temporal disaggregation configuration
disaggregation:
  # Time range for disaggregation (UTC hours)
  hour_range_start: 12  # 12Z (start of day)
  hour_range_end: 36    # 11Z next day (end of day)
  
  # Output settings
  output_dir: "./data/prism_hourly"
  output_format: "netcdf4"
  
  # Quality control
  quality_control: true
  mass_conservation_check: true
  tolerance: 0.01  # for mass conservation (1%)
  
  # Processing options
  fill_missing_hours: true
  interpolate_gaps: false

# Logging configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  file_path: "./logs/prism_hourly.log"
  max_file_size: "10MB"
  backup_count: 5
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  console_output: true

# Validation configuration
validation:
  # Spatial validation
  check_spatial_consistency: true
  check_grid_alignment: true
  
  # Temporal validation
  check_temporal_consistency: true
  check_time_coverage: true
  
  # Data validation
  check_data_ranges: true
  min_precipitation: 0.0
  max_precipitation: 500.0  # mm/hour
  
  # File validation
  check_file_integrity: true
  verify_checksums: false

# Performance configuration
performance:
  # Parallel processing
  use_multiprocessing: true
  max_workers: 4  # number of parallel processes
  
  # Memory management
  memory_limit: "8GB"
  chunk_processing: true
  
  # Caching
  enable_caching: true
  cache_dir: "./cache"
  cache_size_limit: "1GB"

# Advanced configuration
advanced:
  # Coordinate reference systems
  source_crs: "EPSG:4326"
  target_crs: "EPSG:4326"
  
  # Data formats
  preferred_format: "netcdf4"
  compression_level: 6
  
  # Error handling
  continue_on_error: false
  error_log_detail: true
