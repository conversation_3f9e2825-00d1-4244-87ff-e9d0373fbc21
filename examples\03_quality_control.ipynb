{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PRISM Hourly Dataset - Quality Control and Validation\n", "\n", "This notebook demonstrates comprehensive quality control and validation procedures for the PRISM Hourly Dataset package.\n", "\n", "## Overview\n", "\n", "Quality control is essential for ensuring the reliability of hourly precipitation estimates. This notebook covers:\n", "\n", "1. **Data Validation**: Checking file integrity, metadata, and data ranges\n", "2. **Spatial Consistency**: Verifying grid alignment and coordinate systems\n", "3. **Temporal Consistency**: Ensuring proper time series continuity\n", "4. **Mass Conservation**: Validating that hourly estimates sum to daily totals\n", "5. **Statistical Analysis**: Examining data distributions and outliers\n", "6. **Visualization**: Creating quality control plots and reports"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "from pathlib import Path\n", "import numpy as np\n", "import pandas as pd\n", "import xarray as xr\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import date, datetime, timedelta\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Add the package to the path\n", "sys.path.insert(0, str(Path.cwd().parent / \"src\"))\n", "\n", "# Import package modules\n", "from prism_hourly.utils.config_manager import ConfigManager\n", "from prism_hourly.utils.logging_utils import LoggingUtils\n", "from prism_hourly.utils.validation_utils import ValidationUtils\n", "from prism_hourly.utils.grid_utils import GridUtils\n", "from prism_hourly.data_processing.temporal_disaggregator import TemporalDisaggregator\n", "\n", "# Set up plotting\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Quality control setup complete!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configuration and Logging"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load configuration\n", "config_file = Path.cwd().parent / \"config\" / \"config.yaml\"\n", "config_manager = ConfigManager(str(config_file))\n", "config = config_manager.get_config()\n", "\n", "# Configure logging\n", "LoggingUtils.configure_logging(\n", "    level='INFO',\n", "    console_output=True,\n", "    log_file='quality_control.log'\n", ")\n", "\n", "logger = LoggingUtils.get_logger(__name__)\n", "logger.info(\"Quality control notebook started\")\n", "\n", "print(\"Configuration and logging ready\")\n", "print(f\"Validation tolerance: {config.validation.tolerance}\")\n", "print(f\"Mass conservation tolerance: {config.disaggregation.tolerance}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup Test Data Directories"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define data directories\n", "data_dir = Path(\"./qc_test_data\")\n", "daily_dir = data_dir / \"daily\"\n", "hourly_dir = data_dir / \"hourly\"\n", "reports_dir = data_dir / \"qc_reports\"\n", "\n", "# Create directories\n", "for directory in [daily_dir, hourly_dir, reports_dir]:\n", "    directory.mkdir(parents=True, exist_ok=True)\n", "    print(f\"Created: {directory}\")\n", "\n", "# Initialize validator\n", "validator = ValidationUtils()\n", "\n", "print(\"\\nTest environment ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Dataset Validation\n", "\n", "### File Integrity and Metadata Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def validate_dataset_comprehensive(file_path):\n", "    \"\"\"Perform comprehensive dataset validation.\"\"\"\n", "    \n", "    print(f\"\\n=== Validating: {Path(file_path).name} ===\")\n", "    \n", "    # 1. File integrity check\n", "    print(\"\\n1. File Integrity Check\")\n", "    integrity_results = validator.validate_file_integrity(file_path)\n", "    \n", "    if integrity_results['is_valid']:\n", "        print(\"✓ File integrity: PASSED\")\n", "        print(f\"  File size: {integrity_results['file_size_mb']:.2f} MB\")\n", "        print(f\"  Format: {integrity_results['format']}\")\n", "    else:\n", "        print(\"✗ File integrity: FAILED\")\n", "        for error in integrity_results['errors']:\n", "            print(f\"  Error: {error}\")\n", "    \n", "    # 2. Dataset validation\n", "    print(\"\\n2. Dataset Validation\")\n", "    dataset_results = validator.validate_dataset(\n", "        file_path,\n", "        check_spatial=True,\n", "        check_temporal=True,\n", "        check_data_ranges=True,\n", "        check_metadata=True\n", "    )\n", "    \n", "    if dataset_results['is_valid']:\n", "        print(\"✓ Dataset validation: PASSED\")\n", "    else:\n", "        print(\"✗ Dataset validation: FAILED\")\n", "        \n", "    # Display detailed results\n", "    for check_type, results in dataset_results.items():\n", "        if isinstance(results, dict) and 'is_valid' in results:\n", "            status = \"✓ PASSED\" if results['is_valid'] else \"✗ FAILED\"\n", "            print(f\"  {check_type.title()}: {status}\")\n", "            \n", "            if 'errors' in results and results['errors']:\n", "                for error in results['errors']:\n", "                    print(f\"    Error: {error}\")\n", "                    \n", "            if 'warnings' in results and results['warnings']:\n", "                for warning in results['warnings']:\n", "                    print(f\"    Warning: {warning}\")\n", "    \n", "    return dataset_results\n", "\n", "# Find test files\n", "test_files = list(daily_dir.glob(\"*.nc\")) + list(hourly_dir.glob(\"*.nc\"))\n", "\n", "if test_files:\n", "    # Validate first few files\n", "    validation_results = {}\n", "    for file_path in test_files[:3]:\n", "        results = validate_dataset_comprehensive(str(file_path))\n", "        validation_results[str(file_path)] = results\nelse:\n", "    print(\"No test files found. Creating mock validation example...\")\n", "    print(\"\\nExample validation output:\")\n", "    print(\"=== Validating: PRISM_20230701.nc ===\")\n", "    print(\"\\n1. File Integrity Check\")\n", "    print(\"✓ File integrity: PASSED\")\n", "    print(\"  File size: 15.23 MB\")\n", "    print(\"  Format: NetCDF4\")\n", "    print(\"\\n2. Dataset Validation\")\n", "    print(\"✓ Dataset validation: PASSED\")\n", "    print(\"  Spatial: ✓ PASSED\")\n", "    print(\"  Temporal: ✓ PASSED\")\n", "    print(\"  Data_ranges: ✓ PASSED\")\n", "    print(\"  Metadata: ✓ PASSED\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Spatial Consistency Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_spatial_consistency(file_paths):\n", "    \"\"\"Analyze spatial consistency across multiple files.\"\"\"\n", "    \n", "    print(\"\\n=== Spatial Consistency Analysis ===\")\n", "    \n", "    if not file_paths:\n", "        print(\"No files provided for spatial analysis\")\n", "        return\n", "    \n", "    spatial_info = []\n", "    \n", "    for file_path in file_paths:\n", "        try:\n", "            with xr.open_dataset(file_path) as ds:\n", "                info = {\n", "                    'file': Path(file_path).name,\n", "                    'shape': ds.dims,\n", "                    'lat_min': float(ds.lat.min()),\n", "                    'lat_max': float(ds.lat.max()),\n", "                    'lon_min': float(ds.lon.min()),\n", "                    'lon_max': float(ds.lon.max()),\n", "                    'lat_res': float(ds.lat.diff('lat').mean()),\n", "                    'lon_res': float(ds.lon.diff('lon').mean())\n", "                }\n", "                spatial_info.append(info)\n", "                \n", "        except Exception as e:\n", "            print(f\"Error reading {file_path}: {e}\")\n", "    \n", "    if not spatial_info:\n", "        print(\"No valid spatial information found\")\n", "        return\n", "    \n", "    # Convert to DataFrame for analysis\n", "    df = pd.DataFrame(spatial_info)\n", "    \n", "    print(f\"\\nAnalyzed {len(df)} files:\")\n", "    print(\"\\nSpatial Grid Summary:\")\n", "    print(df[['file', 'lat_min', 'lat_max', 'lon_min', 'lon_max']].to_string(index=False))\n", "    \n", "    # Check consistency\n", "    print(\"\\nConsistency Checks:\")\n", "    \n", "    # Check if all grids have same bounds\n", "    lat_min_consistent = df['lat_min'].nunique() == 1\n", "    lat_max_consistent = df['lat_max'].nunique() == 1\n", "    lon_min_consistent = df['lon_min'].nunique() == 1\n", "    lon_max_consistent = df['lon_max'].nunique() == 1\n", "    \n", "    print(f\"  Latitude bounds consistent: {'✓' if lat_min_consistent and lat_max_consistent else '✗'}\")\n", "    print(f\"  Longitude bounds consistent: {'✓' if lon_min_consistent and lon_max_consistent else '✗'}\")\n", "    \n", "    # Check resolution consistency\n", "    lat_res_consistent = df['lat_res'].std() < 1e-6\n", "    lon_res_consistent = df['lon_res'].std() < 1e-6\n", "    \n", "    print(f\"  Latitude resolution consistent: {'✓' if lat_res_consistent else '✗'}\")\n", "    print(f\"  Longitude resolution consistent: {'✓' if lon_res_consistent else '✗'}\")\n", "    \n", "    if lat_res_consistent and lon_res_consistent:\n", "        print(f\"  Grid resolution: {df['lat_res'].mean():.6f}° lat, {df['lon_res'].mean():.6f}° lon\")\n", "    \n", "    return df\n", "\n", "# Analyze spatial consistency\n", "if test_files:\n", "    spatial_df = analyze_spatial_consistency(test_files)\nelse:\n", "    print(\"Creating mock spatial consistency analysis...\")\n", "    print(\"\\n=== Spatial Consistency Analysis ===\")\n", "    print(\"\\nAnalyzed 3 files:\")\n", "    print(\"\\nSpatial Grid Summary:\")\n", "    print(\"                file  lat_min  lat_max   lon_min   lon_max\")\n", "    print(\"  PRISM_20230701.nc    25.0625   49.9375  -124.9375  -66.0625\")\n", "    print(\"  PRISM_20230702.nc    25.0625   49.9375  -124.9375  -66.0625\")\n", "    print(\"  PRISM_20230703.nc    25.0625   49.9375  -124.9375  -66.0625\")\n", "    print(\"\\nConsistency Checks:\")\n", "    print(\"  Latitude bounds consistent: ✓\")\n", "    print(\"  Longitude bounds consistent: ✓\")\n", "    print(\"  Latitude resolution consistent: ✓\")\n", "    print(\"  Longitude resolution consistent: ✓\")\n", "    print(\"  Grid resolution: 0.041667° lat, 0.041667° lon\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Mass Conservation Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def validate_mass_conservation(daily_file, hourly_files, tolerance=0.01):\n", "    \"\"\"Validate mass conservation between daily and hourly data.\"\"\"\n", "    \n", "    print(\"\\n=== Mass Conservation Validation ===\")\n", "    \n", "    if not daily_file or not hourly_files:\n", "        print(\"Insufficient data for mass conservation check\")\n", "        print(\"Need both daily and hourly files\")\n", "        return\n", "    \n", "    try:\n", "        # Load daily data\n", "        with xr.open_dataset(daily_file) as daily_ds:\n", "            daily_precip = daily_ds['precipitation'].isel(time=0)\n", "            \n", "        print(f\"Daily file: {Path(daily_file).name}\")\n", "        print(f\"Hourly files: {len(hourly_files)} files\")\n", "        \n", "        # Load and sum hourly data\n", "        hourly_sum = None\n", "        valid_hours = 0\n", "        \n", "        for hourly_file in hourly_files:\n", "            try:\n", "                with xr.open_dataset(hourly_file) as hourly_ds:\n", "                    hourly_precip = hourly_ds['precipitation'].isel(time=0)\n", "                    \n", "                    if hourly_sum is None:\n", "                        hourly_sum = hourly_precip.copy()\n", "                    else:\n", "                        hourly_sum += hourly_precip\n", "                    \n", "                    valid_hours += 1\n", "                    \n", "            except Exception as e:\n", "                print(f\"Error reading {hourly_file}: {e}\")\n", "        \n", "        if hourly_sum is None:\n", "            print(\"No valid hourly data found\")\n", "            return\n", "        \n", "        print(f\"Successfully loaded {valid_hours} hourly files\")\n", "        \n", "        # Calculate differences\n", "        diff = hourly_sum - daily_precip\n", "        abs_diff = np.abs(diff)\n", "        \n", "        # Calculate statistics\n", "        valid_mask = ~np.isnan(daily_precip) & ~np.isnan(hourly_sum)\n", "        valid_daily = daily_precip.values[valid_mask]\n", "        valid_hourly = hourly_sum.values[valid_mask]\n", "        valid_diff = diff.values[valid_mask]\n", "        valid_abs_diff = abs_diff.values[valid_mask]\n", "        \n", "        if len(valid_daily) == 0:\n", "            print(\"No valid data points for comparison\")\n", "            return\n", "        \n", "        # Calculate relative differences\n", "        nonzero_mask = valid_daily > 0\n", "        if np.any(nonzero_mask):\n", "            rel_diff = valid_diff[nonzero_mask] / valid_daily[nonzero_mask]\n", "            rel_abs_diff = valid_abs_diff[nonzero_mask] / valid_daily[nonzero_mask]\n", "        else:\n", "            rel_diff = np.array([])\n", "            rel_abs_diff = np.array([])\n", "        \n", "        # Print statistics\n", "        print(f\"\\nMass Conservation Statistics:\")\n", "        print(f\"  Valid grid points: {len(valid_daily):,}\")\n", "        print(f\"  Daily total: {np.sum(valid_daily):.2f} mm\")\n", "        print(f\"  Hourly total: {np.sum(valid_hourly):.2f} mm\")\n", "        print(f\"  Absolute difference: {np.sum(valid_abs_diff):.2f} mm\")\n", "        print(f\"  Mean absolute error: {np.mean(valid_abs_diff):.4f} mm\")\n", "        print(f\"  Max absolute error: {np.max(valid_abs_diff):.4f} mm\")\n", "        \n", "        if len(rel_abs_diff) > 0:\n", "            print(f\"  Mean relative error: {np.mean(rel_abs_diff)*100:.2f}%\")\n", "            print(f\"  Max relative error: {np.max(rel_abs_diff)*100:.2f}%\")\n", "        \n", "        # Check tolerance\n", "        tolerance_met = np.all(valid_abs_diff <= tolerance)\n", "        print(f\"\\nTolerance Check (±{tolerance} mm):\")\n", "        print(f\"  Status: {'✓ PASSED' if tolerance_met else '✗ FAILED'}\")\n", "        \n", "        if not tolerance_met:\n", "            exceeding_points = np.sum(valid_abs_diff > tolerance)\n", "            print(f\"  Points exceeding tolerance: {exceeding_points:,} ({exceeding_points/len(valid_daily)*100:.1f}%)\")\n", "        \n", "        # Create visualization\n", "        fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "        \n", "        # Scatter plot\n", "        axes[0,0].scatter(valid_daily, valid_hourly, alpha=0.5, s=1)\n", "        axes[0,0].plot([0, np.max(valid_daily)], [0, np.max(valid_daily)], 'r--', label='Perfect conservation')\n", "        axes[0,0].set_xlabel('Daily Precipitation (mm)')\n", "        axes[0,0].set_ylabel('Hourly Sum (mm)')\n", "        axes[0,0].set_title('Mass Conservation Scatter Plot')\n", "        axes[0,0].legend()\n", "        axes[0,0].grid(True, alpha=0.3)\n", "        \n", "        # Difference histogram\n", "        axes[0,1].hist(valid_diff, bins=50, alpha=0.7, edgecolor='black')\n", "        axes[0,1].axvline(0, color='red', linestyle='--', label='Perfect conservation')\n", "        axes[0,1].set_xlabel('Difference (Hourly - Daily) mm')\n", "        axes[0,1].set_ylabel('Frequency')\n", "        axes[0,1].set_title('Mass Conservation Differences')\n", "        axes[0,1].legend()\n", "        axes[0,1].grid(True, alpha=0.3)\n", "        \n", "        # Spatial difference map\n", "        im = axes[1,0].imshow(diff.values, cmap='RdBu_r', vmin=-tolerance*2, vmax=tolerance*2)\n", "        axes[1,0].set_title('Spatial Differences (Hourly - Daily)')\n", "        axes[1,0].set_xlabel('Grid X')\n", "        axes[1,0].set_ylabel('Grid Y')\n", "        plt.colorbar(im, ax=axes[1,0], label='Difference (mm)')\n", "        \n", "        # Relative error for non-zero precipitation\n", "        if len(rel_diff) > 0:\n", "            axes[1,1].hist(rel_diff*100, bins=50, alpha=0.7, edgecolor='black')\n", "            axes[1,1].axvline(0, color='red', linestyle='--', label='Perfect conservation')\n", "            axes[1,1].set_xlabel('Relative Difference (%)')\n", "            axes[1,1].set_ylabel('Frequency')\n", "            axes[1,1].set_title('Relative Differences (Non-zero Precipitation)')\n", "            axes[1,1].legend()\n", "            axes[1,1].grid(True, alpha=0.3)\n", "        else:\n", "            axes[1,1].text(0.5, 0.5, 'No non-zero precipitation\\nfor relative analysis', \n", "                          ha='center', va='center', transform=axes[1,1].transAxes)\n", "            axes[1,1].set_title('Relative Differences')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        return {\n", "            'tolerance_met': tolerance_met,\n", "            'mean_abs_error': np.mean(valid_abs_diff),\n", "            'max_abs_error': np.max(valid_abs_diff),\n", "            'total_daily': np.sum(valid_daily),\n", "            'total_hourly': np.sum(valid_hourly),\n", "            'valid_points': len(valid_daily)\n", "        }\n", "        \n", "    except Exception as e:\n", "        print(f\"Error in mass conservation validation: {e}\")\n", "        return None\n", "\n", "# Find daily and hourly files for the same date\n", "daily_files = list(daily_dir.glob(\"*.nc\"))\n", "hourly_files = list(hourly_dir.glob(\"*.nc\"))\n", "\n", "if daily_files and hourly_files:\n", "    # Use first daily file and corresponding hourly files\n", "    conservation_results = validate_mass_conservation(\n", "        str(daily_files[0]), \n", "        [str(f) for f in hourly_files[:24]],  # First 24 hours\n", "        tolerance=config.disaggregation.tolerance\n", "    )\nelse:\n", "    print(\"Creating mock mass conservation validation...\")\n", "    print(\"\\n=== Mass Conservation Validation ===\")\n", "    print(\"Daily file: PRISM_20230701.nc\")\n", "    print(\"Hourly files: 24 files\")\n", "    print(\"Successfully loaded 24 hourly files\")\n", "    print(\"\\nMass Conservation Statistics:\")\n", "    print(\"  Valid grid points: 621,025\")\n", "    print(\"  Daily total: 15,432.67 mm\")\n", "    print(\"  Hourly total: 15,432.71 mm\")\n", "    print(\"  Absolute difference: 0.04 mm\")\n", "    print(\"  Mean absolute error: 0.0001 mm\")\n", "    print(\"  Max absolute error: 0.0089 mm\")\n", "    print(\"  Mean relative error: 0.02%\")\n", "    print(\"  Max relative error: 0.15%\")\n", "    print(\"\\nTolerance Check (±0.01 mm):\")\n", "    print(\"  Status: ✓ PASSED\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}