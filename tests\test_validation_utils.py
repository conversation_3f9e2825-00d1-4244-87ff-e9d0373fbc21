"""
Tests for the ValidationUtils class.
"""

import pytest
import numpy as np
import xarray as xr
from pathlib import Path
import tempfile
import sys

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from prism_hourly.utils.validation_utils import ValidationUtils
from tests.conftest import assert_valid_netcdf, assert_mass_conservation, assert_grid_consistency


class TestValidationUtils:
    """Test cases for ValidationUtils."""
    
    def test_validate_dataset_valid_file(self, sample_prism_file):
        """Test validation of a valid dataset."""
        validator = ValidationUtils()
        
        results = validator.validate_dataset(
            sample_prism_file,
            check_spatial=True,
            check_temporal=True,
            check_data_ranges=True,
            check_metadata=True
        )
        
        assert results['is_valid'] is True
        assert 'spatial' in results
        assert 'temporal' in results
        assert 'data_ranges' in results
        assert 'metadata' in results
        
        # Check that all sub-validations passed
        assert results['spatial']['is_valid'] is True
        assert results['temporal']['is_valid'] is True
        assert results['data_ranges']['is_valid'] is True
        assert results['metadata']['is_valid'] is True
    
    def test_validate_dataset_invalid_file(self, temp_dir):
        """Test validation of an invalid dataset."""
        validator = ValidationUtils()
        
        # Create an invalid file (not NetCDF)
        invalid_file = temp_dir / "invalid.txt"
        with open(invalid_file, 'w') as f:
            f.write("This is not a NetCDF file")
        
        results = validator.validate_dataset(str(invalid_file))
        
        assert results['is_valid'] is False
        assert len(results['errors']) > 0
    
    def test_validate_file_integrity_valid(self, sample_prism_file):
        """Test file integrity validation for valid file."""
        validator = ValidationUtils()
        
        results = validator.validate_file_integrity(sample_prism_file)
        
        assert results['is_valid'] is True
        assert results['file_exists'] is True
        assert results['file_size_mb'] > 0
        assert results['format'] == 'NetCDF'
        assert len(results['errors']) == 0
    
    def test_validate_file_integrity_nonexistent(self):
        """Test file integrity validation for non-existent file."""
        validator = ValidationUtils()
        
        results = validator.validate_file_integrity("nonexistent_file.nc")
        
        assert results['is_valid'] is False
        assert results['file_exists'] is False
        assert len(results['errors']) > 0
    
    def test_validate_spatial_consistency(self, sample_prism_file):
        """Test spatial consistency validation."""
        validator = ValidationUtils()
        
        with xr.open_dataset(sample_prism_file) as ds:
            results = validator._validate_spatial(ds)
        
        assert results['is_valid'] is True
        assert 'grid_info' in results
        assert 'lat_range' in results['grid_info']
        assert 'lon_range' in results['grid_info']
        assert 'shape' in results['grid_info']
        
        # Check that lat/lon ranges are reasonable
        lat_range = results['grid_info']['lat_range']
        lon_range = results['grid_info']['lon_range']
        
        assert -90 <= lat_range[0] <= lat_range[1] <= 90
        assert -180 <= lon_range[0] <= lon_range[1] <= 180
    
    def test_validate_temporal_consistency(self, sample_prism_file):
        """Test temporal consistency validation."""
        validator = ValidationUtils()
        
        with xr.open_dataset(sample_prism_file) as ds:
            results = validator._validate_temporal(ds)
        
        assert results['is_valid'] is True
        assert 'time_info' in results
        assert 'time_range' in results['time_info']
        assert 'time_steps' in results['time_info']
    
    def test_validate_data_ranges(self, sample_prism_file):
        """Test data range validation."""
        validator = ValidationUtils()
        
        with xr.open_dataset(sample_prism_file) as ds:
            results = validator._validate_data_ranges(ds)
        
        assert results['is_valid'] is True
        assert 'data_stats' in results
        
        # Check precipitation statistics
        precip_stats = results['data_stats']['precipitation']
        assert 'min' in precip_stats
        assert 'max' in precip_stats
        assert 'mean' in precip_stats
        assert 'valid_count' in precip_stats
        
        # Precipitation should be non-negative
        assert precip_stats['min'] >= 0
    
    def test_validate_metadata(self, sample_prism_file):
        """Test metadata validation."""
        validator = ValidationUtils()
        
        with xr.open_dataset(sample_prism_file) as ds:
            results = validator._validate_metadata(ds)
        
        assert results['is_valid'] is True
        assert 'required_attrs' in results
        assert 'variable_attrs' in results
    
    def test_validate_mass_conservation_perfect(self, sample_prism_data, sample_hourly_files):
        """Test mass conservation validation with perfect conservation."""
        validator = ValidationUtils()
        
        # Create daily file
        with tempfile.NamedTemporaryFile(suffix='.nc', delete=False) as f:
            daily_file = f.name
            sample_prism_data.to_netcdf(daily_file)
        
        try:
            # For this test, we'll create hourly files that sum exactly to daily
            # This is a simplified test - in practice, the hourly files would be
            # created by the disaggregation process
            
            results = validator.validate_mass_conservation(
                daily_file,
                sample_hourly_files[:1],  # Use just one hourly file for simplicity
                tolerance=0.01
            )
            
            assert 'is_valid' in results
            assert 'conservation_stats' in results
            
        finally:
            Path(daily_file).unlink()
    
    def test_validate_mass_conservation_violation(self, temp_dir):
        """Test mass conservation validation with conservation violation."""
        validator = ValidationUtils()
        
        # Create daily data
        daily_data = xr.Dataset({
            'precipitation': (['time', 'lat', 'lon'], [[[10.0, 20.0], [30.0, 40.0]]])
        }, coords={
            'time': [np.datetime64('2023-07-01')],
            'lat': [35.0, 36.0],
            'lon': [-120.0, -119.0]
        })
        
        # Create hourly data that doesn't conserve mass
        hourly_data = xr.Dataset({
            'precipitation': (['time', 'lat', 'lon'], [[[5.0, 10.0], [15.0, 25.0]]])  # Half the daily amount
        }, coords={
            'time': [np.datetime64('2023-07-01T12:00')],
            'lat': [35.0, 36.0],
            'lon': [-120.0, -119.0]
        })
        
        # Save files
        daily_file = temp_dir / "daily.nc"
        hourly_file = temp_dir / "hourly.nc"
        
        daily_data.to_netcdf(daily_file)
        hourly_data.to_netcdf(hourly_file)
        
        results = validator.validate_mass_conservation(
            str(daily_file),
            [str(hourly_file)],
            tolerance=0.01
        )
        
        # Should fail conservation check
        assert results['is_valid'] is False
        assert 'conservation_stats' in results
    
    def test_validate_directory_structure(self, temp_dir):
        """Test directory structure validation."""
        validator = ValidationUtils()
        
        # Create some test files
        (temp_dir / "file1.nc").touch()
        (temp_dir / "file2.nc").touch()
        (temp_dir / "subdir").mkdir()
        (temp_dir / "subdir" / "file3.nc").touch()
        
        results = validator.validate_directory_structure(str(temp_dir))
        
        assert results['is_valid'] is True
        assert 'file_count' in results
        assert 'directory_info' in results
        assert results['file_count'] >= 2
    
    def test_validate_grid_consistency_matching(self, sample_prism_data, sample_ncep_data):
        """Test grid consistency validation with matching grids."""
        validator = ValidationUtils()
        
        # Both sample datasets should have the same grid
        results = validator._validate_grid_consistency(sample_prism_data, sample_ncep_data)
        
        assert results['is_valid'] is True
        assert 'grid_match' in results
        assert results['grid_match'] is True
    
    def test_validate_grid_consistency_mismatched(self, sample_prism_data):
        """Test grid consistency validation with mismatched grids."""
        validator = ValidationUtils()
        
        # Create data with different grid
        different_grid_data = xr.Dataset({
            'precipitation': (['time', 'lat', 'lon'], [[[1.0, 2.0], [3.0, 4.0], [5.0, 6.0]]])
        }, coords={
            'time': [np.datetime64('2023-07-01')],
            'lat': [30.0, 31.0, 32.0],  # Different lat grid
            'lon': [-125.0, -124.0]     # Different lon grid
        })
        
        results = validator._validate_grid_consistency(sample_prism_data, different_grid_data)
        
        assert results['is_valid'] is False
        assert 'grid_match' in results
        assert results['grid_match'] is False
    
    def test_validate_coordinate_system(self, sample_prism_data):
        """Test coordinate system validation."""
        validator = ValidationUtils()
        
        results = validator._validate_coordinate_system(sample_prism_data)
        
        assert results['is_valid'] is True
        assert 'crs_info' in results
    
    def test_validate_data_quality_with_outliers(self, temp_dir):
        """Test data quality validation with outliers."""
        validator = ValidationUtils()
        
        # Create data with outliers
        data_with_outliers = np.array([[[1.0, 2.0, 1000.0], [3.0, 4.0, -100.0]]])  # Extreme values
        
        dataset = xr.Dataset({
            'precipitation': (['time', 'lat', 'lon'], data_with_outliers)
        }, coords={
            'time': [np.datetime64('2023-07-01')],
            'lat': [35.0, 36.0],
            'lon': [-120.0, -119.0, -118.0]
        })
        
        file_path = temp_dir / "outliers.nc"
        dataset.to_netcdf(file_path)
        
        results = validator.validate_dataset(str(file_path), check_data_ranges=True)
        
        # Should detect outliers
        assert 'data_ranges' in results
        # The validation might pass or fail depending on the outlier detection thresholds
    
    def test_validate_missing_data_handling(self, temp_dir):
        """Test validation of datasets with missing data."""
        validator = ValidationUtils()
        
        # Create data with NaN values
        data_with_nan = np.array([[[1.0, np.nan, 3.0], [np.nan, 5.0, 6.0]]])
        
        dataset = xr.Dataset({
            'precipitation': (['time', 'lat', 'lon'], data_with_nan)
        }, coords={
            'time': [np.datetime64('2023-07-01')],
            'lat': [35.0, 36.0],
            'lon': [-120.0, -119.0, -118.0]
        })
        
        file_path = temp_dir / "missing_data.nc"
        dataset.to_netcdf(file_path)
        
        results = validator.validate_dataset(str(file_path))
        
        assert results['is_valid'] is True  # NaN values should be acceptable
        assert 'data_ranges' in results
    
    def test_custom_validation_tolerances(self, sample_prism_file):
        """Test validation with custom tolerances."""
        # Create validator with custom tolerances
        validator = ValidationUtils(tolerance=1e-3)
        
        results = validator.validate_dataset(sample_prism_file)
        
        assert results['is_valid'] is True
    
    def test_validation_error_reporting(self, temp_dir):
        """Test that validation errors are properly reported."""
        validator = ValidationUtils()
        
        # Create a file with multiple issues
        problematic_data = xr.Dataset({
            'precipitation': (['time', 'lat', 'lon'], [[[np.inf, -np.inf, np.nan]]])
        }, coords={
            'time': [np.datetime64('2023-07-01')],
            'lat': [91.0],  # Invalid latitude
            'lon': [-181.0]  # Invalid longitude
        })
        
        file_path = temp_dir / "problematic.nc"
        problematic_data.to_netcdf(file_path)
        
        results = validator.validate_dataset(str(file_path))
        
        # Should report multiple errors
        assert len(results['errors']) > 0
        assert any('latitude' in error.lower() or 'longitude' in error.lower() 
                  for error in results['errors'])
    
    def test_validation_performance(self, sample_prism_file):
        """Test validation performance with timing."""
        import time
        
        validator = ValidationUtils()
        
        start_time = time.time()
        results = validator.validate_dataset(sample_prism_file)
        end_time = time.time()
        
        # Validation should complete reasonably quickly
        assert end_time - start_time < 10.0  # Less than 10 seconds
        assert results['is_valid'] is True
