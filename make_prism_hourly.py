import os
from datetime import datetime, timedelta
import pandas as pd
import xarray as xr
import numpy as np
import matplotlib.pyplot as plt

# Directories for your PRISM and NCEP files
prism_dir = './output_Prism_daily/'
ncep_dir = './NCEP_StageIV/Hourly_StageIV_data/'

# List all PRISM files
prism_files = sorted([f for f in os.listdir(prism_dir) if f.startswith("PRISM_")])

# Function to get corresponding NCEP file names
def get_ncep_files_for_prism(prism_filename):
#     try:
        # Extract the date from the PRISM file name (YYYYMMDD19)
        date_str = prism_filename.split('_')[1][0:8]  # Ensure extracting first 8 characters after 'PRISM_'
        prism_date = datetime(int(date_str[0:4]), int(date_str[4:6]), int(date_str[6:]), 0, 0, 0, 0)
        
        # Create list to hold NCEP file names
        ncep_files = []
        
        # Loop through the specified hours
        for hour in range(12, 36):  # 12 to 11 on the next day
            # Calculate the NCEP date and hour
            ncep_date = prism_date + timedelta(hours=hour)
            ncep_date_str = ncep_date.strftime("%Y%m%d%H")
            
            # Format the NCEP file name
            ncep_filename = f"NCEP_StageIV_data_precip2_{ncep_date_str}.nc"
        
            # Check if the file exists, and add to list if it does
            if os.path.exists(os.path.join(ncep_dir, ncep_filename)):
                ncep_files.append(ncep_filename)
            else:
                print(f"Warning: NCEP file {ncep_filename} does not exist.")
        
        return ncep_files
#     except ValueError:
#         print(f"Skipping file {prism_filename}: Could not parse date.")
#         return []

# Process each PRISM file and get corresponding NCEP files
for k,prism_file in enumerate(prism_files):
    try:
        prism_filepath = os.path.join(prism_dir, prism_file)

        # Get the NCEP files for this PRISM file
        ncep_files = get_ncep_files_for_prism(prism_file)

        # Print or process the results as needed
        if ncep_files:
            print(f"PRISM file: {prism_file}")
            print(f"Corresponding NCEP files: {ncep_files}")
            print("-" * 40)

        # Directories for your PRISM daily and NCEP hourly files
        daily_dir = prism_dir
        ncep_dir = ncep_dir
        output_dir = './hourly_prism_2023_2024/'

        daily_file = prism_file 
        # Open the PRISM daily file
        daily_ds = xr.open_dataset(os.path.join(daily_dir, daily_file))

        # Calculate total daily value at each grid cell from the PRISM data
        daily_total = daily_ds['precipitation']  # Replace 'precip' with the actual variable name if different

        # Sum up the 24-hourly NCEP files for the day
        ncep_hourly_datasets = [xr.open_dataset(os.path.join(ncep_dir, f))['precipitation'] for f in ncep_files]

        ncep_hourly_sum = np.nansum(ncep_hourly_datasets,axis=0)  # Sum all hourly data for the same day

        # Process each NCEP hourly file
        for i, ncep_file in enumerate(ncep_files):
            # Extract hour information from the filename
            date_str = ncep_file.split('_')[-1][0:8]
            hour_str = ncep_file.split('_')[-1][8:10]

            # Open the specific hourly file again to maintain independence
            ncep_ds = xr.open_dataset(os.path.join(ncep_dir, ncep_file))
            # Calculate the ratio for this hour
            hourly_ratio = ncep_ds['precipitation'][0] / ncep_hourly_sum[0]
            
            
            # Adjust hourly precipitation by multiplying the ratio with the PRISM daily total
            adjusted_hourly_precip = hourly_ratio * daily_total

            # Create a new dataset to store the adjusted hourly data
            adjusted_ds = ncep_ds.copy()
            adjusted_hourly_precip = adjusted_hourly_precip.fillna(0.0).where(adjusted_hourly_precip >= 0, 0.0)
            adjusted_ds['precipitation'][0,:,:] = adjusted_hourly_precip[:,:,0]  # Ensure variable name matches

            # Save the adjusted dataset as a new NetCDF file
            output_filename = f'PRISM_{date_str}{hour_str}.nc'
            # Remove one or both attributes before saving
            adjusted_ds['precipitation'].encoding.clear()

            # Now save
            adjusted_ds.to_netcdf(os.path.join(output_dir, output_filename), format='NETCDF4')
            
            # Close the current hourly dataset
            ncep_ds.close()

        # Close datasets and free resources
        daily_ds.close()
        for ds in ncep_hourly_datasets:
            ds.close()
    except:
        pass
