"""
Pytest configuration and shared fixtures for PRISM Hourly Dataset tests.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
import numpy as np
import xarray as xr
from datetime import date, datetime
import yaml
import os
import sys

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from prism_hourly.utils.config_manager import ConfigManager


@pytest.fixture(scope="session")
def temp_dir():
    """Create a temporary directory for test files."""
    temp_path = Path(tempfile.mkdtemp())
    yield temp_path
    shutil.rmtree(temp_path)


@pytest.fixture(scope="session")
def test_config_file(temp_dir):
    """Create a test configuration file."""
    config_data = {
        'download': {
            'prism_base_url': 'https://data.prism.oregonstate.edu/daily/ppt',
            'ncep_base_url': 'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4',
            'download_delay': 1.0,
            'max_retries': 2,
            'timeout': 30,
            'user_agent': 'PRISM-Hourly-Dataset/1.0'
        },
        'processing': {
            'resolution': 4000.0,
            'target_crs': 'EPSG:4326',
            'nodata_value': -9999.0,
            'resampling_method': 'cubic',
            'compress_output': True,
            'chunk_size': 1000
        },
        'disaggregation': {
            'hour_range_start': 12,
            'hour_range_end': 36,
            'tolerance': 0.01,
            'quality_control': True,
            'interpolation_method': 'linear',
            'fill_missing_hours': True
        },
        'logging': {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file_rotation': True,
            'max_file_size_mb': 10,
            'backup_count': 5
        },
        'validation': {
            'tolerance': 1e-6,
            'check_spatial': True,
            'check_temporal': True,
            'check_data_ranges': True,
            'check_metadata': True,
            'min_precipitation': 0.0,
            'max_precipitation': 1000.0
        },
        'performance': {
            'max_workers': 4,
            'chunk_size': 100,
            'memory_limit_gb': 8,
            'enable_caching': True,
            'cache_size_mb': 1000
        },
        'advanced': {
            'grid_definition_file': None,
            'custom_bounds': None,
            'enable_debugging': False,
            'preserve_intermediate_files': False
        }
    }
    
    config_file = temp_dir / "test_config.yaml"
    with open(config_file, 'w') as f:
        yaml.dump(config_data, f, default_flow_style=False)
    
    return str(config_file)


@pytest.fixture
def test_config(test_config_file):
    """Load test configuration."""
    config_manager = ConfigManager(test_config_file)
    return config_manager.get_config()


@pytest.fixture
def sample_prism_data():
    """Create sample PRISM-like NetCDF data for testing."""
    # Create sample grid (small for testing)
    lats = np.linspace(35.0, 40.0, 50)
    lons = np.linspace(-120.0, -115.0, 60)
    time = [datetime(2023, 7, 1, 12, 0)]
    
    # Create sample precipitation data with realistic patterns
    np.random.seed(42)  # For reproducible tests
    precip_data = np.random.exponential(scale=2.0, size=(1, len(lats), len(lons)))
    precip_data[precip_data > 50] = 0  # Set some areas to no precipitation
    
    # Create xarray dataset
    ds = xr.Dataset(
        {
            'precipitation': (['time', 'lat', 'lon'], precip_data),
        },
        coords={
            'time': time,
            'lat': lats,
            'lon': lons,
        },
        attrs={
            'title': 'Test PRISM Daily Precipitation',
            'source': 'Test data for PRISM Hourly Dataset package',
            'units': 'mm',
            'grid_resolution': '4km',
            'projection': 'WGS84'
        }
    )
    
    return ds


@pytest.fixture
def sample_ncep_data():
    """Create sample NCEP-like NetCDF data for testing."""
    # Create sample grid (same as PRISM for testing)
    lats = np.linspace(35.0, 40.0, 50)
    lons = np.linspace(-120.0, -115.0, 60)
    
    # Create 24 hours of data
    times = [datetime(2023, 7, 1, hour) for hour in range(24)]
    
    # Create sample hourly precipitation data
    np.random.seed(123)  # Different seed for NCEP data
    hourly_data = []
    
    for hour in range(24):
        # Create temporal pattern (more rain in afternoon/evening)
        time_factor = 1.0 + 0.5 * np.sin((hour - 6) * np.pi / 12)
        if time_factor < 0:
            time_factor = 0.1
            
        hour_precip = np.random.exponential(scale=0.5 * time_factor, size=(len(lats), len(lons)))
        hour_precip[hour_precip > 10] = 0  # Set some areas to no precipitation
        hourly_data.append(hour_precip)
    
    hourly_data = np.array(hourly_data)
    
    # Create xarray dataset
    ds = xr.Dataset(
        {
            'precipitation': (['time', 'lat', 'lon'], hourly_data),
        },
        coords={
            'time': times,
            'lat': lats,
            'lon': lons,
        },
        attrs={
            'title': 'Test NCEP Stage IV Hourly Precipitation',
            'source': 'Test data for PRISM Hourly Dataset package',
            'units': 'mm/hr',
            'grid_resolution': '4km',
            'projection': 'WGS84'
        }
    )
    
    return ds


@pytest.fixture
def sample_prism_file(temp_dir, sample_prism_data):
    """Create a sample PRISM NetCDF file."""
    file_path = temp_dir / "PRISM_20230701.nc"
    sample_prism_data.to_netcdf(file_path)
    return str(file_path)


@pytest.fixture
def sample_ncep_file(temp_dir, sample_ncep_data):
    """Create a sample NCEP NetCDF file."""
    file_path = temp_dir / "NCEP_20230701.nc"
    sample_ncep_data.to_netcdf(file_path)
    return str(file_path)


@pytest.fixture
def sample_hourly_files(temp_dir, sample_ncep_data):
    """Create sample hourly NetCDF files."""
    file_paths = []
    
    for hour in range(24):
        # Extract single hour
        hour_data = sample_ncep_data.isel(time=hour)
        
        # Create filename
        file_path = temp_dir / f"PRISM_2023070{1:02d}{hour:02d}.nc"
        
        # Save file
        hour_data.to_netcdf(file_path)
        file_paths.append(str(file_path))
    
    return file_paths


@pytest.fixture
def mock_prism_zip_file(temp_dir):
    """Create a mock PRISM ZIP file for testing downloads."""
    import zipfile
    
    # Create a simple text file to put in the ZIP
    text_file = temp_dir / "PRISM_ppt_stable_4kmD2_20230701_bil.bil"
    with open(text_file, 'w') as f:
        f.write("Mock PRISM BIL data")
    
    # Create header file
    hdr_file = temp_dir / "PRISM_ppt_stable_4kmD2_20230701_bil.hdr"
    with open(hdr_file, 'w') as f:
        f.write("BYTEORDER I\nLAYOUT BIL\nNROWS 50\nNCOLS 60\n")
    
    # Create ZIP file
    zip_file = temp_dir / "PRISM_ppt_stable_4kmD2_20230701_bil.zip"
    with zipfile.ZipFile(zip_file, 'w') as zf:
        zf.write(text_file, text_file.name)
        zf.write(hdr_file, hdr_file.name)
    
    # Clean up temporary files
    text_file.unlink()
    hdr_file.unlink()
    
    return str(zip_file)


@pytest.fixture
def sample_grid_bounds():
    """Sample grid bounds for testing."""
    return (-120.0, 35.0, -115.0, 40.0)  # west, south, east, north


@pytest.fixture
def sample_grid_shape():
    """Sample grid shape for testing."""
    return (50, 60)  # height, width


@pytest.fixture
def mock_download_response():
    """Mock HTTP response for testing downloads."""
    class MockResponse:
        def __init__(self, content=b"mock data", status_code=200, headers=None):
            self.content = content
            self.status_code = status_code
            self.headers = headers or {}
            
        def raise_for_status(self):
            if self.status_code >= 400:
                raise Exception(f"HTTP {self.status_code}")
                
        def iter_content(self, chunk_size=1024):
            for i in range(0, len(self.content), chunk_size):
                yield self.content[i:i + chunk_size]
    
    return MockResponse


@pytest.fixture(autouse=True)
def setup_test_environment(temp_dir):
    """Set up test environment variables."""
    # Set temporary directory for tests
    os.environ['PRISM_HOURLY_TEST_DIR'] = str(temp_dir)
    
    yield
    
    # Clean up
    if 'PRISM_HOURLY_TEST_DIR' in os.environ:
        del os.environ['PRISM_HOURLY_TEST_DIR']


# Test data validation helpers
def assert_valid_netcdf(file_path):
    """Assert that a file is a valid NetCDF file."""
    try:
        with xr.open_dataset(file_path) as ds:
            assert 'precipitation' in ds.data_vars
            assert 'lat' in ds.coords
            assert 'lon' in ds.coords
            assert 'time' in ds.coords
    except Exception as e:
        pytest.fail(f"Invalid NetCDF file {file_path}: {e}")


def assert_mass_conservation(daily_data, hourly_data_list, tolerance=0.01):
    """Assert that mass is conserved between daily and hourly data."""
    daily_total = daily_data.sum()
    hourly_total = sum(data.sum() for data in hourly_data_list)
    
    diff = abs(daily_total - hourly_total)
    assert diff <= tolerance, f"Mass conservation failed: difference = {diff}, tolerance = {tolerance}"


def assert_grid_consistency(data1, data2):
    """Assert that two datasets have consistent grids."""
    assert data1.lat.shape == data2.lat.shape, "Latitude dimensions don't match"
    assert data1.lon.shape == data2.lon.shape, "Longitude dimensions don't match"
    
    np.testing.assert_allclose(data1.lat.values, data2.lat.values, rtol=1e-6)
    np.testing.assert_allclose(data1.lon.values, data2.lon.values, rtol=1e-6)
