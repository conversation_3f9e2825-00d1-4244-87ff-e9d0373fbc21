# PRISM Hourly Dataset Examples

This directory contains comprehensive examples demonstrating how to use the PRISM Hourly Dataset package for creating hourly precipitation estimates.

## Overview

The examples are organized to provide both basic and advanced usage patterns, covering all major package functionality including data download, processing, temporal disaggregation, and validation.

## Example Files

### Python Scripts

#### `basic_usage_example.py`
**Purpose**: Demonstrates the basic workflow for creating hourly precipitation estimates.

**Features**:
- Configuration management
- PRISM and NCEP data downloading
- Data processing and format conversion
- Temporal disaggregation
- Data validation
- Error handling

**Usage**:
```bash
cd examples
python basic_usage_example.py
```

**What it does**:
1. Loads configuration from YAML file
2. Downloads PRISM daily data for a small date range
3. Processes PRISM files to NetCDF format
4. Demonstrates temporal disaggregation concepts
5. Validates output data quality
6. Provides comprehensive logging

#### `advanced_processing_example.py`
**Purpose**: Showcases advanced features and optimization techniques.

**Features**:
- Custom grid definitions
- Parallel processing with multiprocessing
- Advanced quality control options
- Performance monitoring and optimization
- Error recovery mechanisms
- Comprehensive reporting

**Usage**:
```bash
cd examples
python advanced_processing_example.py
```

**What it does**:
1. Creates custom spatial grids
2. Processes multiple files in parallel
3. Implements advanced validation workflows
4. Monitors performance metrics
5. Generates detailed processing reports
6. Demonstrates error recovery strategies

### Jupyter Notebooks

#### `01_basic_usage.ipynb`
**Purpose**: Interactive tutorial for package basics.

**Features**:
- Step-by-step workflow demonstration
- Data visualization examples
- Interactive configuration management
- Real-time validation feedback
- Educational content with explanations

**Usage**:
```bash
cd examples
jupyter notebook 01_basic_usage.ipynb
```

**Sections**:
1. Setup and imports
2. Configuration management
3. Data download examples
4. Data processing workflow
5. Visualization and analysis
6. Validation and quality control
7. Temporal disaggregation concepts

### Shell Scripts

#### `cli_usage_examples.sh`
**Purpose**: Comprehensive CLI usage demonstrations.

**Features**:
- All CLI command examples
- Batch processing workflows
- Custom configuration usage
- Error handling patterns
- Production-ready scripts

**Usage**:
```bash
cd examples
chmod +x cli_usage_examples.sh
./cli_usage_examples.sh
```

**Commands demonstrated**:
- `prism-hourly download prism` - Download PRISM data
- `prism-hourly download ncep` - Download NCEP data
- `prism-hourly process prism` - Process PRISM files
- `prism-hourly disaggregate` - Create hourly estimates
- `prism-hourly validate dataset` - Validate data files
- `prism-hourly validate conservation` - Check mass conservation
- `prism-hourly config` - Configuration management

## Getting Started

### Prerequisites

1. **Install the package**:
   ```bash
   cd ..  # Go to package root
   pip install -e .
   ```

2. **Install additional dependencies for examples**:
   ```bash
   pip install jupyter matplotlib
   ```

3. **Verify installation**:
   ```bash
   prism-hourly --help
   ```

### Quick Start

1. **Run the basic example**:
   ```bash
   cd examples
   python basic_usage_example.py
   ```

2. **Open the Jupyter notebook**:
   ```bash
   jupyter notebook 01_basic_usage.ipynb
   ```

3. **Try CLI commands**:
   ```bash
   ./cli_usage_examples.sh
   ```

## Example Workflows

### Workflow 1: Basic Data Processing

```bash
# 1. Download PRISM data
prism-hourly download prism --start-date 2023-07-01 --end-date 2023-07-03

# 2. Process to common grid
prism-hourly process prism --input-dir ./data/prism_raw --output-dir ./data/prism_processed

# 3. Validate processed data
prism-hourly validate dataset ./data/prism_processed/PRISM_20230701.nc
```

### Workflow 2: Complete Hourly Dataset Creation

```bash
# 1. Download both PRISM and NCEP data
prism-hourly download prism --start-date 2023-07-01 --end-date 2023-07-31
prism-hourly download ncep --start-year 2023 --end-year 2023 --extract

# 2. Process both datasets
prism-hourly process prism --input-dir ./data/prism_raw --output-dir ./data/prism_processed

# 3. Create hourly estimates
prism-hourly disaggregate \
    --prism-dir ./data/prism_processed \
    --ncep-dir ./data/ncep_processed \
    --output-dir ./data/prism_hourly

# 4. Validate mass conservation
prism-hourly validate conservation \
    --daily-file ./data/prism_processed/PRISM_20230701.nc \
    --hourly-dir ./data/prism_hourly
```

### Workflow 3: Custom Configuration

```bash
# 1. Copy and modify configuration
cp ../config/config.yaml my_config.yaml
# Edit my_config.yaml as needed

# 2. Use custom configuration
prism-hourly --config my_config.yaml download prism --start-date 2023-07-01 --end-date 2023-07-03

# 3. Validate configuration
prism-hourly --config my_config.yaml config
```

## Data Requirements

### Input Data

1. **PRISM Daily Precipitation**:
   - Source: PRISM Climate Group, Oregon State University
   - Format: BIL files in ZIP archives
   - Resolution: 4km daily
   - Coverage: Continental United States

2. **NCEP Stage IV Radar Data**:
   - Source: NCEP/NWS Stage IV precipitation analysis
   - Format: GRIB files in TAR archives
   - Resolution: 4km hourly
   - Coverage: Continental United States

### Storage Requirements

- **PRISM data**: ~1GB per year
- **NCEP data**: ~100GB per year
- **Processed hourly estimates**: ~50GB per year

## Output Data

### File Structure

```
output_directory/
├── prism_raw/           # Downloaded PRISM ZIP files
├── ncep_raw/            # Downloaded NCEP TAR files
├── prism_processed/     # Processed PRISM NetCDF files
├── ncep_processed/      # Processed NCEP NetCDF files
└── prism_hourly/        # Final hourly estimates
```

### File Naming Convention

- **PRISM daily**: `PRISM_YYYYMMDD.nc`
- **NCEP hourly**: `NCEP_YYYYMMDDHH.nc`
- **Hourly estimates**: `PRISM_YYYYMMDDHH.nc`

## Configuration

### Default Configuration

The examples use the default configuration file at `../config/config.yaml`. Key settings:

```yaml
download:
  prism_base_url: "https://data.prism.oregonstate.edu/daily/ppt"
  ncep_base_url: "https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4"
  download_delay: 2.0

processing:
  resolution: 4000.0
  resampling_method: "cubic"
  compress_output: true

disaggregation:
  hour_range_start: 12
  hour_range_end: 36
  tolerance: 0.01
```

### Custom Configuration

Create custom configuration files for different use cases:

```bash
# Copy default configuration
cp ../config/config.yaml custom_config.yaml

# Edit as needed
nano custom_config.yaml

# Use with any command
prism-hourly --config custom_config.yaml <command>
```

## Troubleshooting

### Common Issues

1. **Download failures**:
   - Check internet connection
   - Verify server availability
   - Increase timeout settings
   - Check available disk space

2. **Processing errors**:
   - Verify input file formats
   - Check grid definition files
   - Ensure sufficient memory
   - Validate coordinate systems

3. **Memory issues**:
   - Reduce processing chunk size
   - Enable data compression
   - Process smaller date ranges
   - Use parallel processing carefully

### Debug Mode

Enable verbose logging for troubleshooting:

```bash
prism-hourly --verbose --log-level DEBUG <command>
```

### Log Files

Examples create log files for detailed debugging:
- `basic_usage_example.log`
- `advanced_processing_example.log`
- `notebook_example.log`

## Performance Tips

### Optimization Strategies

1. **Parallel Processing**:
   - Use multiprocessing for large datasets
   - Configure worker count based on system resources
   - Monitor memory usage during parallel operations

2. **Memory Management**:
   - Process data in chunks
   - Enable output compression
   - Close datasets after use

3. **Storage Optimization**:
   - Use SSD storage for temporary files
   - Compress output files
   - Clean up intermediate files

### Benchmarking

The advanced example includes performance monitoring:
- Processing time per file
- Memory usage tracking
- Validation time measurement
- Overall workflow timing

## Contributing

### Adding New Examples

1. Create new example files following naming conventions
2. Include comprehensive documentation
3. Add error handling and logging
4. Update this README with new examples
5. Test examples with different configurations

### Example Guidelines

- Include clear documentation and comments
- Demonstrate error handling
- Provide realistic use cases
- Show both basic and advanced usage
- Include performance considerations

## Support

For questions about the examples:

1. Check the main package documentation
2. Review the configuration files
3. Examine log files for errors
4. Open an issue on GitHub
5. Consult the API documentation

## Additional Resources

- **Main Documentation**: `../README.md`
- **Configuration Reference**: `../config/config.yaml`
- **API Documentation**: `../docs/`
- **Test Suite**: `../tests/`

## License

These examples are part of the PRISM Hourly Dataset package and are licensed under the MIT License.
