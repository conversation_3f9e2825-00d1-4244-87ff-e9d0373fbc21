#!/bin/bash

# Directory to store final extracted data
output_dir="final_data"
mkdir -p "$output_dir"

# Loop through each top-level .tar file
for outer_tar in *.tar; do
    # Create a temporary working directory for this file
    work_dir="${outer_tar%.tar}_work"
    mkdir -p "$work_dir"
    cd "$work_dir" || exit 1

    # Step 1: Extract the outer .tar file
    tar -xf "../$outer_tar"

    # Step 2: Assume the extracted file is a tar file without extension
    inner_file=$(ls)
    mv "$inner_file" "${inner_file}.tar"

    # Step 3: Extract the renamed inner tar file
    tar -xf "${inner_file}.tar"

    #This might work or it might not. If it does, we may not have .Z files that we have to worry about. The results varied for me. Either way, you will get files for 01h, 06h, 24h.
    # Step 4: Find and decompress all .Z files
    find . -type f -name "*.Z" -exec gzip -d {} \;

    # Step 5: Move all resulting files to the final output directory
    find . -type f -exec mv {} "../$output_dir/" \;

    # Clean up and return to base directory
    cd ..
    rm -rf "$work_dir"
done
