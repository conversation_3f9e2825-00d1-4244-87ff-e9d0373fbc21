[tool:pytest]
# Pytest configuration for PRISM Hourly Dataset package

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Output options
addopts =
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --disable-warnings
    --color=yes
    --durations=10

# Markers for test categorization
markers =
    unit: Unit tests for individual components
    integration: Integration tests for component interactions
    slow: Tests that take a long time to run
    network: Tests that require network access
    cli: Tests for command-line interface
    config: Tests for configuration management
    validation: Tests for data validation
    processing: Tests for data processing
    download: Tests for data download functionality
    disaggregation: Tests for temporal disaggregation

# Minimum version requirements
minversion = 6.0

# Test collection
collect_ignore =
    setup.py
    build
    dist
    .eggs

# Warnings
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::FutureWarning

# Coverage options (if pytest-cov is installed)
# addopts = --cov=prism_hourly --cov-report=html --cov-report=term-missing

# Timeout for tests (if pytest-timeout is installed)
# timeout = 300

# Parallel execution (if pytest-xdist is installed)
# addopts = -n auto
