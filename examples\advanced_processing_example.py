#!/usr/bin/env python3
"""
Advanced processing example for the PRISM Hourly Dataset package.

This script demonstrates advanced features including:
- Custom grid definitions
- Parallel processing
- Quality control customization
- Performance optimization
- Error handling and recovery
"""

import os
import sys
from pathlib import Path
from datetime import date, datetime
import logging
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import time

# Add the package to the path for development
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from prism_hourly.utils.config_manager import ConfigManager
from prism_hourly.utils.logging_utils import LoggingUtils
from prism_hourly.utils.grid_utils import GridUtils
from prism_hourly.data_processing.data_processor import DataProcessor
from prism_hourly.data_processing.temporal_disaggregator import TemporalDisaggregator
from prism_hourly.utils.validation_utils import ValidationUtils


class AdvancedProcessor:
    """Advanced processing class with custom configurations and optimizations."""
    
    def __init__(self, config_file: str):
        """Initialize the advanced processor."""
        self.config_manager = ConfigManager(config_file)
        self.config = self.config_manager.get_config()
        self.logger = LoggingUtils.get_logger(__name__)
        
        # Setup performance monitoring
        self.start_time = time.time()
        self.processing_stats = {
            'files_processed': 0,
            'files_failed': 0,
            'total_processing_time': 0,
            'validation_time': 0
        }
    
    def create_custom_grid(self, bounds, resolution_km):
        """Create a custom grid definition."""
        self.logger.info(f"Creating custom grid with {resolution_km}km resolution")
        
        # Calculate grid dimensions
        west, south, east, north = bounds
        
        # Convert resolution from km to degrees (approximate)
        lat_res_deg = resolution_km / 111.0  # 1 degree ≈ 111 km
        lon_res_deg = resolution_km / (111.0 * 0.8)  # Approximate for mid-latitudes
        
        width = int((east - west) / lon_res_deg)
        height = int((north - south) / lat_res_deg)
        
        # Create coordinate arrays
        lons, lats = GridUtils.create_coordinate_arrays(bounds, (height, width))
        
        grid_info = {
            'bounds': bounds,
            'shape': (height, width),
            'resolution_km': resolution_km,
            'lons': lons,
            'lats': lats
        }
        
        self.logger.info(f"Custom grid created: {width}x{height} cells")
        return grid_info
    
    def process_file_with_validation(self, file_path, output_dir, custom_grid=None):
        """Process a single file with comprehensive validation."""
        start_time = time.time()
        
        try:
            # Initialize processor with custom settings
            processor = DataProcessor(
                resolution=self.config.processing.resolution,
                nodata_value=self.config.processing.nodata_value,
                resampling_method='cubic',  # Higher quality resampling
                compress_output=True
            )
            
            # Process the file
            if custom_grid:
                # Use custom grid if provided
                output_file = processor.process_prism_file_to_grid(
                    file_path, output_dir, custom_grid
                )
            else:
                # Use standard processing
                output_file = processor.process_prism_file(file_path, output_dir)
            
            # Validate the output
            validator = ValidationUtils()
            validation_start = time.time()
            
            validation_results = validator.validate_dataset(
                output_file,
                check_spatial=True,
                check_temporal=True,
                check_data_ranges=True,
                check_metadata=True
            )
            
            validation_time = time.time() - validation_start
            processing_time = time.time() - start_time
            
            result = {
                'file_path': file_path,
                'output_file': output_file,
                'processing_time': processing_time,
                'validation_time': validation_time,
                'validation_results': validation_results,
                'success': validation_results['is_valid']
            }
            
            if validation_results['is_valid']:
                self.logger.info(f"✓ Successfully processed {Path(file_path).name}")
            else:
                self.logger.warning(f"⚠ Processed {Path(file_path).name} with validation issues")
                for error in validation_results['errors']:
                    self.logger.warning(f"  Error: {error}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"✗ Failed to process {Path(file_path).name}: {e}")
            return {
                'file_path': file_path,
                'output_file': None,
                'processing_time': time.time() - start_time,
                'validation_time': 0,
                'validation_results': None,
                'success': False,
                'error': str(e)
            }
    
    def parallel_processing(self, input_files, output_dir, max_workers=None):
        """Process multiple files in parallel."""
        if max_workers is None:
            max_workers = min(mp.cpu_count(), len(input_files))
        
        self.logger.info(f"Starting parallel processing with {max_workers} workers")
        self.logger.info(f"Processing {len(input_files)} files")
        
        results = []
        
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_file = {
                executor.submit(self.process_file_with_validation, file_path, output_dir): file_path
                for file_path in input_files
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    # Update statistics
                    if result['success']:
                        self.processing_stats['files_processed'] += 1
                    else:
                        self.processing_stats['files_failed'] += 1
                    
                    self.processing_stats['total_processing_time'] += result['processing_time']
                    self.processing_stats['validation_time'] += result['validation_time']
                    
                except Exception as e:
                    self.logger.error(f"Task failed for {file_path}: {e}")
                    results.append({
                        'file_path': file_path,
                        'success': False,
                        'error': str(e)
                    })
                    self.processing_stats['files_failed'] += 1
        
        return results
    
    def advanced_disaggregation(self, prism_dir, ncep_dir, output_dir):
        """Perform temporal disaggregation with advanced options."""
        self.logger.info("Starting advanced temporal disaggregation")
        
        # Custom disaggregation settings
        disaggregator = TemporalDisaggregator(
            prism_dir=prism_dir,
            ncep_dir=ncep_dir,
            output_dir=output_dir,
            hour_range=(self.config.disaggregation.hour_range_start,
                       self.config.disaggregation.hour_range_end),
            quality_control=True,
            mass_conservation_tolerance=0.005,  # Stricter tolerance
            fill_missing_hours=True,
            interpolation_method='cubic',  # Higher quality interpolation
            chunk_size=100  # Process in chunks for memory efficiency
        )
        
        # Enable performance monitoring
        disaggregator.enable_performance_monitoring()
        
        # Process with error recovery
        try:
            stats = disaggregator.process_all_prism_files()
            
            self.logger.info("Disaggregation completed successfully")
            self.logger.info(f"Statistics: {stats}")
            
            # Get performance metrics
            performance_metrics = disaggregator.get_performance_metrics()
            self.logger.info(f"Performance metrics: {performance_metrics}")
            
            return stats, performance_metrics
            
        except Exception as e:
            self.logger.error(f"Disaggregation failed: {e}")
            
            # Attempt recovery by processing individual files
            self.logger.info("Attempting recovery with individual file processing")
            recovery_stats = disaggregator.process_with_recovery()
            
            return recovery_stats, None
    
    def comprehensive_validation_suite(self, data_dir):
        """Run comprehensive validation on all output data."""
        self.logger.info("Running comprehensive validation suite")
        
        validator = ValidationUtils()
        validation_results = {}
        
        # Find all NetCDF files
        nc_files = list(Path(data_dir).rglob("*.nc"))
        
        if not nc_files:
            self.logger.warning("No NetCDF files found for validation")
            return validation_results
        
        self.logger.info(f"Validating {len(nc_files)} files")
        
        for nc_file in nc_files:
            self.logger.info(f"Validating {nc_file.name}")
            
            # Dataset validation
            dataset_results = validator.validate_dataset(str(nc_file))
            
            # File integrity check
            integrity_results = validator.validate_file_integrity(str(nc_file))
            
            # Directory structure validation
            dir_results = validator.validate_directory_structure(str(nc_file.parent))
            
            validation_results[str(nc_file)] = {
                'dataset': dataset_results,
                'integrity': integrity_results,
                'directory': dir_results
            }
            
            # Log summary
            if all([dataset_results['is_valid'], integrity_results['is_valid'], 
                   dir_results['is_valid']]):
                self.logger.info(f"✓ {nc_file.name} passed all validations")
            else:
                self.logger.warning(f"⚠ {nc_file.name} has validation issues")
        
        return validation_results
    
    def generate_processing_report(self, results, validation_results):
        """Generate a comprehensive processing report."""
        self.logger.info("Generating processing report")
        
        total_time = time.time() - self.start_time
        
        report = {
            'processing_summary': {
                'total_files': len(results),
                'successful': sum(1 for r in results if r['success']),
                'failed': sum(1 for r in results if not r['success']),
                'total_time_seconds': total_time,
                'average_processing_time': self.processing_stats['total_processing_time'] / len(results) if results else 0,
                'total_validation_time': self.processing_stats['validation_time']
            },
            'performance_metrics': self.processing_stats,
            'validation_summary': {
                'files_validated': len(validation_results),
                'validation_passed': sum(1 for v in validation_results.values() 
                                       if v['dataset']['is_valid']),
                'validation_failed': sum(1 for v in validation_results.values() 
                                       if not v['dataset']['is_valid'])
            }
        }
        
        # Log report summary
        self.logger.info("=== PROCESSING REPORT ===")
        self.logger.info(f"Total files processed: {report['processing_summary']['total_files']}")
        self.logger.info(f"Successful: {report['processing_summary']['successful']}")
        self.logger.info(f"Failed: {report['processing_summary']['failed']}")
        self.logger.info(f"Total processing time: {total_time:.2f} seconds")
        self.logger.info(f"Average time per file: {report['processing_summary']['average_processing_time']:.2f} seconds")
        self.logger.info(f"Validation passed: {report['validation_summary']['validation_passed']}/{report['validation_summary']['files_validated']}")
        
        return report


def main():
    """Main function demonstrating advanced processing features."""
    
    # Configure logging with performance monitoring
    LoggingUtils.configure_logging(
        level='INFO',
        console_output=True,
        log_file='advanced_processing_example.log',
        enable_performance_logging=True
    )
    
    logger = LoggingUtils.get_logger(__name__)
    logger.info("Starting advanced processing example")
    
    # Initialize advanced processor
    config_file = Path(__file__).parent.parent / "config" / "config.yaml"
    processor = AdvancedProcessor(str(config_file))
    
    # Create output directories
    data_dir = Path("./advanced_example_data")
    input_dir = data_dir / "input"
    output_dir = data_dir / "output"
    hourly_dir = data_dir / "hourly"
    
    for directory in [input_dir, output_dir, hourly_dir]:
        directory.mkdir(parents=True, exist_ok=True)
    
    # Example 1: Custom grid creation
    logger.info("Example 1: Custom grid creation")
    
    # Define custom bounds (smaller region for example)
    custom_bounds = (-125.0, 32.0, -114.0, 42.0)  # California region
    custom_grid = processor.create_custom_grid(custom_bounds, resolution_km=2.0)
    
    # Example 2: Parallel processing with validation
    logger.info("Example 2: Parallel processing with validation")
    
    # Find input files (would be PRISM zip files in real usage)
    input_files = list(input_dir.glob("*.zip"))
    
    if input_files:
        # Process files in parallel
        results = processor.parallel_processing(
            input_files, 
            str(output_dir), 
            max_workers=4
        )
        
        logger.info(f"Parallel processing completed: {len(results)} files processed")
    else:
        logger.info("No input files found - skipping parallel processing example")
        results = []
    
    # Example 3: Advanced disaggregation (requires data)
    logger.info("Example 3: Advanced disaggregation")
    
    # This would require actual PRISM and NCEP data
    # stats, metrics = processor.advanced_disaggregation(
    #     str(output_dir), 
    #     "./ncep_data", 
    #     str(hourly_dir)
    # )
    
    # Example 4: Comprehensive validation
    logger.info("Example 4: Comprehensive validation suite")
    
    validation_results = processor.comprehensive_validation_suite(str(data_dir))
    
    # Example 5: Generate processing report
    logger.info("Example 5: Generate processing report")
    
    report = processor.generate_processing_report(results, validation_results)
    
    # Save report to file
    import json
    report_file = data_dir / "processing_report.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"Processing report saved to: {report_file}")
    logger.info("Advanced processing example completed")


if __name__ == "__main__":
    main()
