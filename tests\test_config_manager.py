"""
Tests for the ConfigManager class.
"""

import pytest
import tempfile
import yaml
from pathlib import Path
import sys

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from prism_hourly.utils.config_manager import ConfigMana<PERSON>, DownloadConfig, ProcessingConfig


class TestConfigManager:
    """Test cases for ConfigManager."""
    
    def test_load_valid_config(self, test_config_file):
        """Test loading a valid configuration file."""
        config_manager = ConfigManager(test_config_file)
        config = config_manager.get_config()
        
        # Test that config is loaded
        assert config is not None
        assert hasattr(config, 'download')
        assert hasattr(config, 'processing')
        assert hasattr(config, 'disaggregation')
        
        # Test specific values
        assert config.download.prism_base_url == 'https://data.prism.oregonstate.edu/daily/ppt'
        assert config.processing.resolution == 4000.0
        assert config.disaggregation.hour_range_start == 12
    
    def test_load_nonexistent_config(self):
        """Test loading a non-existent configuration file."""
        with pytest.raises(FileNotFoundError):
            ConfigManager("nonexistent_config.yaml")
    
    def test_invalid_yaml_config(self, temp_dir):
        """Test loading an invalid YAML file."""
        invalid_config = temp_dir / "invalid_config.yaml"
        with open(invalid_config, 'w') as f:
            f.write("invalid: yaml: content: [")
        
        with pytest.raises(yaml.YAMLError):
            ConfigManager(str(invalid_config))
    
    def test_validate_config(self, test_config_file):
        """Test configuration validation."""
        config_manager = ConfigManager(test_config_file)
        
        # Valid config should pass validation
        assert config_manager.validate_config() is True
    
    def test_invalid_config_validation(self, temp_dir):
        """Test validation of invalid configuration."""
        # Create config with invalid values
        invalid_config_data = {
            'download': {
                'prism_base_url': '',  # Invalid empty URL
                'download_delay': -1.0,  # Invalid negative delay
            },
            'processing': {
                'resolution': 0,  # Invalid zero resolution
            }
        }
        
        invalid_config_file = temp_dir / "invalid_config.yaml"
        with open(invalid_config_file, 'w') as f:
            yaml.dump(invalid_config_data, f)
        
        config_manager = ConfigManager(str(invalid_config_file))
        
        # Should fail validation
        assert config_manager.validate_config() is False
    
    def test_get_section(self, test_config_file):
        """Test getting specific configuration sections."""
        config_manager = ConfigManager(test_config_file)
        
        # Test getting download config
        download_config = config_manager.get_section('download')
        assert isinstance(download_config, DownloadConfig)
        assert download_config.prism_base_url == 'https://data.prism.oregonstate.edu/daily/ppt'
        
        # Test getting processing config
        processing_config = config_manager.get_section('processing')
        assert isinstance(processing_config, ProcessingConfig)
        assert processing_config.resolution == 4000.0
    
    def test_get_invalid_section(self, test_config_file):
        """Test getting a non-existent configuration section."""
        config_manager = ConfigManager(test_config_file)
        
        with pytest.raises(KeyError):
            config_manager.get_section('nonexistent_section')
    
    def test_update_config(self, test_config_file, temp_dir):
        """Test updating configuration values."""
        config_manager = ConfigManager(test_config_file)
        
        # Update a value
        config_manager.update_config('download.download_delay', 5.0)
        
        # Verify the update
        config = config_manager.get_config()
        assert config.download.download_delay == 5.0
        
        # Save updated config
        new_config_file = temp_dir / "updated_config.yaml"
        config_manager.save_config(str(new_config_file))
        
        # Load the saved config and verify
        new_config_manager = ConfigManager(str(new_config_file))
        new_config = new_config_manager.get_config()
        assert new_config.download.download_delay == 5.0
    
    def test_config_dataclass_validation(self):
        """Test dataclass validation for configuration sections."""
        # Test valid DownloadConfig
        valid_download = DownloadConfig(
            prism_base_url='https://example.com',
            ncep_base_url='https://example.com',
            download_delay=1.0,
            max_retries=3,
            timeout=30,
            user_agent='test'
        )
        assert valid_download.prism_base_url == 'https://example.com'
        
        # Test ProcessingConfig
        valid_processing = ProcessingConfig(
            resolution=4000.0,
            target_crs='EPSG:4326',
            nodata_value=-9999.0,
            resampling_method='cubic',
            compress_output=True,
            chunk_size=1000
        )
        assert valid_processing.resolution == 4000.0
    
    def test_config_defaults(self, temp_dir):
        """Test that default values are properly set."""
        # Create minimal config
        minimal_config = {
            'download': {
                'prism_base_url': 'https://example.com'
            }
        }
        
        config_file = temp_dir / "minimal_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(minimal_config, f)
        
        config_manager = ConfigManager(str(config_file))
        config = config_manager.get_config()
        
        # Check that defaults are applied
        assert config.download.download_delay == 2.0  # Default value
        assert config.download.max_retries == 3  # Default value
    
    def test_config_environment_override(self, test_config_file, monkeypatch):
        """Test configuration override from environment variables."""
        # Set environment variable
        monkeypatch.setenv('PRISM_HOURLY_DOWNLOAD_DELAY', '10.0')
        
        config_manager = ConfigManager(test_config_file)
        
        # Check if environment variable overrides config
        # Note: This test assumes environment override functionality exists
        # If not implemented, this test documents the expected behavior
        config = config_manager.get_config()
        
        # This would pass if environment override is implemented
        # assert config.download.download_delay == 10.0
    
    def test_config_serialization(self, test_config_file, temp_dir):
        """Test configuration serialization and deserialization."""
        config_manager = ConfigManager(test_config_file)
        original_config = config_manager.get_config()
        
        # Save config to new file
        new_config_file = temp_dir / "serialized_config.yaml"
        config_manager.save_config(str(new_config_file))
        
        # Load from new file
        new_config_manager = ConfigManager(str(new_config_file))
        loaded_config = new_config_manager.get_config()
        
        # Compare configurations
        assert original_config.download.prism_base_url == loaded_config.download.prism_base_url
        assert original_config.processing.resolution == loaded_config.processing.resolution
        assert original_config.disaggregation.tolerance == loaded_config.disaggregation.tolerance
    
    def test_config_merge(self, test_config_file, temp_dir):
        """Test merging configurations."""
        # Create override config
        override_config = {
            'download': {
                'download_delay': 5.0,
                'max_retries': 5
            },
            'processing': {
                'resolution': 2000.0
            }
        }
        
        override_file = temp_dir / "override_config.yaml"
        with open(override_file, 'w') as f:
            yaml.dump(override_config, f)
        
        # Load base config
        config_manager = ConfigManager(test_config_file)
        
        # Merge with override (if merge functionality exists)
        # config_manager.merge_config(str(override_file))
        
        # This test documents expected merge behavior
        # The merged config should have override values where specified
        # and original values elsewhere
    
    def test_config_validation_errors(self, temp_dir):
        """Test specific validation error cases."""
        # Test missing required fields
        incomplete_config = {
            'download': {
                # Missing required prism_base_url
            }
        }
        
        config_file = temp_dir / "incomplete_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(incomplete_config, f)
        
        with pytest.raises((KeyError, ValueError)):
            ConfigManager(str(config_file))
    
    def test_config_type_conversion(self, temp_dir):
        """Test automatic type conversion in configuration."""
        # Create config with string numbers
        string_config = {
            'download': {
                'prism_base_url': 'https://example.com',
                'download_delay': '2.5',  # String that should be float
                'max_retries': '3'  # String that should be int
            },
            'processing': {
                'resolution': '4000.0',  # String that should be float
                'compress_output': 'true'  # String that should be bool
            }
        }
        
        config_file = temp_dir / "string_config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(string_config, f)
        
        config_manager = ConfigManager(str(config_file))
        config = config_manager.get_config()
        
        # Check that types are correctly converted
        assert isinstance(config.download.download_delay, float)
        assert config.download.download_delay == 2.5
        assert isinstance(config.download.max_retries, int)
        assert config.download.max_retries == 3
        assert isinstance(config.processing.resolution, float)
        assert config.processing.resolution == 4000.0
