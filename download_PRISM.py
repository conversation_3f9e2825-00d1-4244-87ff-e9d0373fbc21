import datetime
import time
import subprocess

base_url = 'https://data.prism.oregonstate.edu/daily/ppt'
start = datetime.date(2023, 1, 1)
stop = datetime.date(2025, 1, 1)

current = start
while current <= stop:
    year = current.year
    day = current.strftime('%Y%m%d')
    filename = f"PRISM_ppt_stable_4kmD2_{day}_bil.zip"
    url = f"{base_url}/{year}/{filename}"
    subprocess.run(['wget', '--content-disposition', url])
    time.sleep(2)  # be polite to the server
    current += datetime.timedelta(days=1)
