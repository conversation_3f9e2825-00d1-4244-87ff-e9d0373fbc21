"""
Data processing module for PRISM and NCEP data.

This module provides functionality for processing, reprojecting, and converting
precipitation data from various sources to a common format and grid.
"""

import os
import glob
from pathlib import Path
from typing import List, Optional, Tuple, Dict, Any
import logging
import zipfile
import tarfile

import rasterio
from rasterio.warp import calculate_default_transform, reproject, Resampling
import numpy as np
import xarray as xr
from datetime import datetime, timedelta
import pytz

from ..utils.logging_utils import LoggingUtils
from ..utils.grid_utils import GridUtils


class DataProcessor:
    """
    Process and reproject precipitation data to a common grid.
    
    This class handles the processing of both PRISM daily data and NCEP Stage IV
    radar data, converting them to a common format and spatial grid for use in
    temporal disaggregation.
    
    Attributes:
        target_grid_file (Path): Reference grid file for reprojection
        resolution (float): Target resolution in meters
        nodata_value (float): Value to use for missing data
        logger (logging.Logger): Logger instance for this class
    
    Example:
        >>> processor = DataProcessor(
        ...     target_grid_file="./config/Terrain.tif",
        ...     resolution=4000.0
        ... )
        >>> output_file = processor.process_prism_file("PRISM_ppt_20230101.zip")
    """
    
    def __init__(
        self,
        target_grid_file: str = "./config/Terrain.tif",
        resolution: float = 4000.0,
        nodata_value: float = -9999.0,
        resampling_method: str = "cubic",
        compress_output: bool = True
    ):
        """
        Initialize the data processor.
        
        Args:
            target_grid_file: Path to reference grid file for reprojection
            resolution: Target resolution in meters
            nodata_value: Value to use for missing data
            resampling_method: Resampling method for reprojection
            compress_output: Whether to compress output NetCDF files
        """
        self.target_grid_file = Path(target_grid_file)
        self.resolution = resolution
        self.nodata_value = nodata_value
        self.compress_output = compress_output
        
        # Set resampling method
        resampling_methods = {
            "nearest": Resampling.nearest,
            "bilinear": Resampling.bilinear,
            "cubic": Resampling.cubic,
            "cubic_spline": Resampling.cubic_spline
        }
        self.resampling_method = resampling_methods.get(
            resampling_method, Resampling.cubic
        )
        
        # Set up logging
        self.logger = LoggingUtils.get_logger(__name__)
        
        # Load target grid information
        self._load_target_grid_info()
    
    def _load_target_grid_info(self):
        """Load target grid information from the reference file."""
        try:
            with rasterio.open(self.target_grid_file) as target_file:
                self.target_crs = target_file.crs
                self.target_bounds = target_file.bounds
                self.target_transform = target_file.transform
                self.target_shape = (target_file.height, target_file.width)
            
            self.logger.info(f"Loaded target grid: {self.target_shape} at {self.resolution}m resolution")
            
        except Exception as e:
            self.logger.error(f"Failed to load target grid file {self.target_grid_file}: {e}")
            raise
    
    def process_prism_files(
        self, 
        input_dir: str, 
        output_dir: str,
        file_pattern: str = "PRISM_*.zip"
    ) -> List[str]:
        """
        Process multiple PRISM files.
        
        Args:
            input_dir: Directory containing PRISM zip files
            output_dir: Directory to save processed NetCDF files
            file_pattern: Pattern to match PRISM files
            
        Returns:
            List of processed output file paths
        """
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        prism_files = sorted(input_path.glob(file_pattern))
        processed_files = []
        
        self.logger.info(f"Processing {len(prism_files)} PRISM files")
        
        for prism_file in prism_files:
            try:
                output_file = self.process_prism_file(prism_file, output_path)
                if output_file:
                    processed_files.append(output_file)
            except Exception as e:
                self.logger.error(f"Failed to process {prism_file}: {e}")
        
        self.logger.info(f"Successfully processed {len(processed_files)} files")
        return processed_files
    
    def process_prism_file(self, prism_zip_path: str, output_dir: str) -> Optional[str]:
        """
        Process a single PRISM zip file to NetCDF format.
        
        Args:
            prism_zip_path: Path to PRISM zip file
            output_dir: Directory to save processed file
            
        Returns:
            Path to processed NetCDF file if successful, None otherwise
        """
        prism_zip_path = Path(prism_zip_path)
        output_dir = Path(output_dir)
        
        # Extract date from filename
        try:
            date_str = prism_zip_path.stem.split('_')[3]  # PRISM_ppt_stable_4kmD2_YYYYMMDD_bil
            date = datetime.strptime(date_str, '%Y%m%d')
        except (IndexError, ValueError) as e:
            self.logger.error(f"Could not extract date from filename {prism_zip_path}: {e}")
            return None
        
        # Define output filename
        output_filename = f"PRISM_{date_str}.nc"
        output_path = output_dir / output_filename
        
        # Skip if output already exists
        if output_path.exists():
            self.logger.debug(f"Output file already exists: {output_filename}")
            return str(output_path)
        
        try:
            # Open and process the PRISM file
            bil_path = self._get_bil_path_from_zip(prism_zip_path)
            
            with rasterio.open(f"/vsizip/{prism_zip_path}/{bil_path}") as prism_file:
                # Read data and metadata
                prism_data = prism_file.read(1)
                prism_transform = prism_file.transform
                prism_crs = prism_file.crs
                
                # Handle nodata values
                prism_data = np.where(
                    prism_data == prism_file.nodata, 
                    self.nodata_value, 
                    prism_data
                )
                
                # Reproject to target grid
                reprojected_data = self._reproject_data(
                    prism_data, prism_transform, prism_crs
                )
                
                # Create xarray dataset
                dataset = self._create_prism_dataset(reprojected_data, date)
                
                # Save to NetCDF
                encoding = self._get_netcdf_encoding() if self.compress_output else {}
                dataset.to_netcdf(output_path, encoding=encoding)
                
                self.logger.debug(f"Processed PRISM file: {output_filename}")
                return str(output_path)
                
        except Exception as e:
            self.logger.error(f"Error processing PRISM file {prism_zip_path}: {e}")
            return None
    
    def _get_bil_path_from_zip(self, zip_path: Path) -> str:
        """Extract the .bil file path from a PRISM zip file."""
        with zipfile.ZipFile(zip_path, 'r') as zf:
            bil_files = [f for f in zf.namelist() if f.endswith('.bil')]
            if not bil_files:
                raise ValueError(f"No .bil file found in {zip_path}")
            return bil_files[0]
    
    def _reproject_data(
        self, 
        data: np.ndarray, 
        source_transform: rasterio.Affine, 
        source_crs: rasterio.CRS
    ) -> np.ndarray:
        """Reproject data to the target grid."""
        # Create output array
        reprojected_data = np.full(self.target_shape, self.nodata_value, dtype=np.float32)
        
        # Reproject
        reproject(
            source=data,
            destination=reprojected_data,
            src_transform=source_transform,
            src_crs=source_crs,
            dst_transform=self.target_transform,
            dst_crs=self.target_crs,
            resampling=self.resampling_method,
            src_nodata=self.nodata_value,
            dst_nodata=self.nodata_value
        )
        
        return reprojected_data
    
    def _create_prism_dataset(self, data: np.ndarray, date: datetime) -> xr.Dataset:
        """Create an xarray dataset from processed PRISM data."""
        # Create coordinate arrays
        lons, lats = GridUtils.create_coordinate_arrays(
            self.target_bounds, self.target_shape
        )
        
        # Create time coordinate (using UTC, 19:00 represents daily total ending at 7PM local time)
        time_coord = [date.replace(hour=19, minute=0, second=0, tzinfo=pytz.UTC)]
        
        # Create dataset
        dataset = xr.Dataset(
            {
                'precipitation': (
                    ['time', 'lat', 'lon'], 
                    data[np.newaxis, :, :],
                    {
                        'units': 'mm',
                        'long_name': 'Daily precipitation',
                        'standard_name': 'precipitation_amount',
                        '_FillValue': self.nodata_value
                    }
                )
            },
            coords={
                'time': ('time', time_coord),
                'lat': ('lat', lats),
                'lon': ('lon', lons)
            },
            attrs={
                'title': 'PRISM Daily Precipitation',
                'source': 'PRISM Climate Group, Oregon State University',
                'institution': 'PRISM Climate Group',
                'Conventions': 'CF-1.6',
                'creation_date': datetime.now().isoformat(),
                'grid_resolution': f'{self.resolution}m'
            }
        )
        
        return dataset
    
    def _get_netcdf_encoding(self) -> Dict[str, Any]:
        """Get NetCDF encoding settings for compression."""
        return {
            'precipitation': {
                'zlib': True,
                'complevel': 6,
                'shuffle': True,
                '_FillValue': self.nodata_value
            }
        }
