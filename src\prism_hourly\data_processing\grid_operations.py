"""
Grid operations module for spatial data processing.

This module provides functionality for grid operations, coordinate transformations,
and spatial data processing operations used in the PRISM hourly dataset creation.
"""

from pathlib import Path
from typing import Tuple, Optional, Dict, Any
import logging

import numpy as np
import rasterio
from rasterio.warp import calculate_default_transform, reproject, Resampling
from rasterio.transform import from_bounds
import xarray as xr
import pyproj

from ..utils.logging_utils import LoggingUtils


class GridOperations:
    """
    Spatial grid operations and transformations.
    
    This class provides utilities for working with spatial grids, including
    coordinate transformations, reprojection, and grid creation operations.
    
    Attributes:
        logger (logging.Logger): Logger instance for this class
    
    Example:
        >>> grid_ops = GridOperations()
        >>> transform = grid_ops.create_transform_from_bounds(
        ...     (-125.0, 25.0, -67.0, 49.0), (1152, 896)
        ... )
    """
    
    def __init__(self):
        """Initialize the grid operations class."""
        self.logger = LoggingUtils.get_logger(__name__)
    
    @staticmethod
    def create_transform_from_bounds(
        bounds: Tuple[float, float, float, float],
        shape: Tuple[int, int]
    ) -> rasterio.Affine:
        """
        Create an affine transform from bounds and shape.
        
        Args:
            bounds: (west, south, east, north) bounds
            shape: (height, width) of the grid
            
        Returns:
            Affine transform object
        """
        west, south, east, north = bounds
        height, width = shape
        
        return from_bounds(west, south, east, north, width, height)
    
    @staticmethod
    def create_coordinate_arrays(
        bounds: Tuple[float, float, float, float],
        shape: Tuple[int, int]
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Create longitude and latitude coordinate arrays.
        
        Args:
            bounds: (west, south, east, north) bounds
            shape: (height, width) of the grid
            
        Returns:
            Tuple of (longitude, latitude) arrays
        """
        west, south, east, north = bounds
        height, width = shape
        
        # Create coordinate arrays
        lons = np.linspace(west, east, width)
        lats = np.linspace(north, south, height)  # Note: north to south for typical image orientation
        
        return lons, lats
    
    @staticmethod
    def create_meshgrid(
        bounds: Tuple[float, float, float, float],
        shape: Tuple[int, int]
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Create 2D longitude and latitude meshgrids.
        
        Args:
            bounds: (west, south, east, north) bounds
            shape: (height, width) of the grid
            
        Returns:
            Tuple of (longitude_grid, latitude_grid) 2D arrays
        """
        lons, lats = GridOperations.create_coordinate_arrays(bounds, shape)
        lon_grid, lat_grid = np.meshgrid(lons, lats)
        
        return lon_grid, lat_grid
    
    def reproject_array(
        self,
        data: np.ndarray,
        src_transform: rasterio.Affine,
        src_crs: str,
        dst_transform: rasterio.Affine,
        dst_crs: str,
        dst_shape: Tuple[int, int],
        resampling: Resampling = Resampling.cubic,
        src_nodata: Optional[float] = None,
        dst_nodata: Optional[float] = None
    ) -> np.ndarray:
        """
        Reproject a numpy array from one CRS to another.
        
        Args:
            data: Input data array
            src_transform: Source affine transform
            src_crs: Source coordinate reference system
            dst_transform: Destination affine transform
            dst_crs: Destination coordinate reference system
            dst_shape: Shape of destination array (height, width)
            resampling: Resampling method
            src_nodata: Source nodata value
            dst_nodata: Destination nodata value
            
        Returns:
            Reprojected data array
        """
        # Create destination array
        dst_array = np.full(dst_shape, dst_nodata or np.nan, dtype=data.dtype)
        
        # Perform reprojection
        reproject(
            source=data,
            destination=dst_array,
            src_transform=src_transform,
            src_crs=src_crs,
            dst_transform=dst_transform,
            dst_crs=dst_crs,
            resampling=resampling,
            src_nodata=src_nodata,
            dst_nodata=dst_nodata
        )
        
        return dst_array
    
    def calculate_grid_resolution(
        self,
        bounds: Tuple[float, float, float, float],
        shape: Tuple[int, int],
        crs: str = "EPSG:4326"
    ) -> Tuple[float, float]:
        """
        Calculate grid resolution in meters.
        
        Args:
            bounds: (west, south, east, north) bounds
            shape: (height, width) of the grid
            crs: Coordinate reference system
            
        Returns:
            Tuple of (x_resolution, y_resolution) in meters
        """
        west, south, east, north = bounds
        height, width = shape
        
        if crs == "EPSG:4326":
            # For geographic coordinates, calculate resolution at center
            center_lat = (south + north) / 2
            center_lon = (west + east) / 2
            
            # Create transformer to calculate distances
            geod = pyproj.Geod(ellps='WGS84')
            
            # Calculate x resolution (longitude)
            _, _, x_dist = geod.inv(west, center_lat, east, center_lat)
            x_resolution = x_dist / width
            
            # Calculate y resolution (latitude)
            _, _, y_dist = geod.inv(center_lon, south, center_lon, north)
            y_resolution = y_dist / height
            
        else:
            # For projected coordinates, assume units are meters
            x_resolution = (east - west) / width
            y_resolution = (north - south) / height
        
        return x_resolution, y_resolution
    
    def create_grid_definition_file(
        self,
        bounds: Tuple[float, float, float, float],
        shape: Tuple[int, int],
        output_path: str,
        grid_type: str = "lonlat"
    ):
        """
        Create a CDO grid definition file.
        
        Args:
            bounds: (west, south, east, north) bounds
            shape: (height, width) of the grid
            output_path: Path to save grid definition file
            grid_type: Type of grid (lonlat, gaussian, etc.)
        """
        west, south, east, north = bounds
        height, width = shape
        
        # Calculate increments
        x_inc = (east - west) / width
        y_inc = (north - south) / height
        
        grid_definition = f"""gridtype = {grid_type}
xsize    = {width}  # Number of longitude points
ysize    = {height}   # Number of latitude points
xfirst   = {west}  # Westernmost longitude
xinc     = {x_inc}  # Longitude increment
yfirst   = {south}  # Southernmost latitude
yinc     = {y_inc}  # Latitude increment
"""
        
        with open(output_path, 'w') as f:
            f.write(grid_definition)
        
        self.logger.info(f"Created grid definition file: {output_path}")
    
    def validate_grid_consistency(
        self,
        datasets: list,
        tolerance: float = 1e-6
    ) -> bool:
        """
        Validate that multiple datasets have consistent grids.
        
        Args:
            datasets: List of xarray datasets to validate
            tolerance: Tolerance for coordinate comparison
            
        Returns:
            True if all grids are consistent
        """
        if len(datasets) < 2:
            return True
        
        reference = datasets[0]
        ref_lons = reference.lon.values
        ref_lats = reference.lat.values
        
        for i, dataset in enumerate(datasets[1:], 1):
            # Check coordinate arrays
            if not np.allclose(dataset.lon.values, ref_lons, atol=tolerance):
                self.logger.error(f"Dataset {i} longitude coordinates don't match reference")
                return False
            
            if not np.allclose(dataset.lat.values, ref_lats, atol=tolerance):
                self.logger.error(f"Dataset {i} latitude coordinates don't match reference")
                return False
        
        self.logger.info(f"All {len(datasets)} datasets have consistent grids")
        return True
    
    def extract_grid_info(self, dataset: xr.Dataset) -> Dict[str, Any]:
        """
        Extract grid information from an xarray dataset.
        
        Args:
            dataset: Input xarray dataset
            
        Returns:
            Dictionary with grid information
        """
        lons = dataset.lon.values
        lats = dataset.lat.values
        
        grid_info = {
            'bounds': (lons.min(), lats.min(), lons.max(), lats.max()),
            'shape': (len(lats), len(lons)),
            'lon_resolution': np.mean(np.diff(lons)),
            'lat_resolution': np.mean(np.diff(lats)),
            'lon_range': (lons.min(), lons.max()),
            'lat_range': (lats.min(), lats.max())
        }
        
        return grid_info
    
    def regrid_dataset(
        self,
        dataset: xr.Dataset,
        target_lons: np.ndarray,
        target_lats: np.ndarray,
        method: str = "linear"
    ) -> xr.Dataset:
        """
        Regrid a dataset to new coordinates using xarray interpolation.
        
        Args:
            dataset: Input dataset
            target_lons: Target longitude coordinates
            target_lats: Target latitude coordinates
            method: Interpolation method (linear, nearest, cubic)
            
        Returns:
            Regridded dataset
        """
        # Interpolate to new grid
        regridded = dataset.interp(
            lon=target_lons,
            lat=target_lats,
            method=method
        )
        
        return regridded
