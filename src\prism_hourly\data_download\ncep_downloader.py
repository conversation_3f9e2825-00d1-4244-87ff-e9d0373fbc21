"""
NCEP Stage IV radar data downloader module.

This module provides functionality for downloading hourly precipitation data from the
NCEP Stage IV radar precipitation analysis.
"""

import os
import sys
from pathlib import Path
from typing import List, Optional, Dict
import logging
from urllib.request import build_opener
from urllib.error import URLError, HTTPError
import time
from tqdm import tqdm

from ..utils.logging_utils import LoggingUtils


class NCEPDownloader:
    """
    Download NCEP Stage IV radar precipitation data.
    
    The NCEP Stage IV dataset provides hourly precipitation analysis based on
    radar and gauge observations across the United States.
    
    Attributes:
        base_url (str): Base URL for NCEP data downloads
        output_dir (Path): Directory to save downloaded files
        logger (logging.Logger): Logger instance for this class
    
    Example:
        >>> downloader = NCEPDownloader(output_dir="./data/ncep_raw")
        >>> downloader.download_year_range(2023, 2023)
    """
    
    def __init__(
        self,
        output_dir: str = "./data/ncep_raw",
        base_url: str = "https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4",
        max_retries: int = 3,
        timeout: int = 300
    ):
        """
        Initialize the NCEP downloader.
        
        Args:
            output_dir: Directory to save downloaded files
            base_url: Base URL for NCEP Stage IV data
            max_retries: Maximum number of retry attempts for failed downloads
            timeout: Timeout for download requests in seconds
        """
        self.base_url = base_url
        self.output_dir = Path(output_dir)
        self.max_retries = max_retries
        self.timeout = timeout
        
        # Create output directory if it doesn't exist
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Set up logging
        self.logger = LoggingUtils.get_logger(__name__)
        
        # Set up URL opener
        self.opener = build_opener()
    
    def download_year_range(self, start_year: int, end_year: int) -> List[str]:
        """
        Download NCEP Stage IV data for a range of years.
        
        Args:
            start_year: Starting year
            end_year: Ending year (inclusive)
            
        Returns:
            List of successfully downloaded file paths
            
        Raises:
            ValueError: If start_year is after end_year
        """
        if start_year > end_year:
            raise ValueError("start_year must be before or equal to end_year")
        
        self.logger.info(f"Starting NCEP download for years {start_year}-{end_year}")
        
        # Generate list of monthly files to download
        monthly_files = []
        for year in range(start_year, end_year + 1):
            for month in range(1, 13):
                monthly_files.append(f"stage4.{year:04d}{month:02d}.tar")
        
        return self.download_monthly_files(monthly_files)
    
    def download_monthly_files(self, monthly_files: List[str]) -> List[str]:
        """
        Download specific monthly NCEP Stage IV files.
        
        Args:
            monthly_files: List of monthly file names to download
            
        Returns:
            List of successfully downloaded file paths
        """
        self.logger.info(f"Downloading {len(monthly_files)} monthly files")
        
        downloaded_files = []
        
        with tqdm(total=len(monthly_files), desc="Downloading NCEP data") as pbar:
            for filename in monthly_files:
                try:
                    file_path = self.download_single_file(filename)
                    if file_path:
                        downloaded_files.append(file_path)
                        pbar.set_postfix({"Last": filename})
                except Exception as e:
                    self.logger.error(f"Failed to download {filename}: {e}")
                
                pbar.update(1)
        
        self.logger.info(f"Downloaded {len(downloaded_files)} files successfully")
        return downloaded_files
    
    def download_single_file(self, filename: str) -> Optional[str]:
        """
        Download a single NCEP Stage IV file.
        
        Args:
            filename: Name of the file to download
            
        Returns:
            Path to downloaded file if successful, None otherwise
        """
        url = f"{self.base_url}/{filename}"
        output_path = self.output_dir / filename
        
        # Skip if file already exists
        if output_path.exists():
            self.logger.debug(f"File already exists: {filename}")
            return str(output_path)
        
        # Try downloading with retries
        for attempt in range(self.max_retries):
            try:
                self.logger.debug(f"Downloading {filename} (attempt {attempt + 1})")
                
                # Open URL and download
                response = self.opener.open(url, timeout=self.timeout)
                
                # Get file size for progress tracking
                file_size = int(response.headers.get('Content-Length', 0))
                
                with open(output_path, 'wb') as f:
                    downloaded = 0
                    chunk_size = 8192
                    
                    while True:
                        chunk = response.read(chunk_size)
                        if not chunk:
                            break
                        f.write(chunk)
                        downloaded += len(chunk)
                
                self.logger.debug(f"Successfully downloaded {filename}")
                return str(output_path)
                
            except (URLError, HTTPError, OSError) as e:
                self.logger.warning(f"Download attempt {attempt + 1} failed for {filename}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                    
                # Clean up partial download
                if output_path.exists():
                    output_path.unlink()
        
        self.logger.error(f"Failed to download {filename} after {self.max_retries} attempts")
        return None
    
    def generate_file_list(self, start_year: int, end_year: int) -> List[str]:
        """
        Generate a list of NCEP Stage IV files for the specified year range.
        
        Args:
            start_year: Starting year
            end_year: Ending year (inclusive)
            
        Returns:
            List of file names to download
        """
        file_list = []
        for year in range(start_year, end_year + 1):
            for month in range(1, 13):
                filename = f"stage4.{year:04d}{month:02d}.tar"
                file_list.append(filename)
        
        return file_list
    
    def verify_downloads(self, file_list: Optional[List[str]] = None) -> Dict:
        """
        Verify downloaded files.
        
        Args:
            file_list: List of files to verify. If None, verify all .tar files in output_dir
            
        Returns:
            Dictionary with verification results
        """
        if file_list is None:
            file_list = list(self.output_dir.glob("stage4.*.tar"))
        
        results = {
            "total": len(file_list),
            "valid": 0,
            "invalid": 0,
            "missing": 0,
            "invalid_files": []
        }
        
        for file_path in file_list:
            path = Path(file_path)
            if not path.exists():
                results["missing"] += 1
                continue
                
            # Basic validation - check if file is not empty and is a valid tar file
            if path.stat().st_size > 0:
                try:
                    import tarfile
                    with tarfile.open(path, 'r') as tf:
                        tf.getnames()  # Test tar integrity
                    results["valid"] += 1
                except (tarfile.TarError, OSError):
                    results["invalid"] += 1
                    results["invalid_files"].append(str(path))
            else:
                results["invalid"] += 1
                results["invalid_files"].append(str(path))
        
        self.logger.info(f"Verification complete: {results['valid']}/{results['total']} files valid")
        return results
    
    def extract_downloaded_files(self, extract_dir: Optional[str] = None) -> List[str]:
        """
        Extract downloaded tar files.
        
        Args:
            extract_dir: Directory to extract files to. If None, extract to output_dir/extracted
            
        Returns:
            List of extracted file paths
        """
        if extract_dir is None:
            extract_dir = self.output_dir / "extracted"
        else:
            extract_dir = Path(extract_dir)
        
        extract_dir.mkdir(parents=True, exist_ok=True)
        
        tar_files = list(self.output_dir.glob("stage4.*.tar"))
        extracted_files = []
        
        self.logger.info(f"Extracting {len(tar_files)} tar files")
        
        with tqdm(total=len(tar_files), desc="Extracting files") as pbar:
            for tar_path in tar_files:
                try:
                    with tarfile.open(tar_path, 'r') as tf:
                        tf.extractall(path=extract_dir)
                        extracted_files.extend([
                            str(extract_dir / member.name) 
                            for member in tf.getmembers() 
                            if member.isfile()
                        ])
                    pbar.set_postfix({"Last": tar_path.name})
                except Exception as e:
                    self.logger.error(f"Failed to extract {tar_path}: {e}")
                
                pbar.update(1)
        
        self.logger.info(f"Extracted {len(extracted_files)} files")
        return extracted_files
