"""
Grid utilities module.

This module provides utility functions for working with spatial grids,
coordinate systems, and grid-related operations.
"""

from pathlib import Path
from typing import Tuple, List, Dict, Any, Optional, Union
import logging

import numpy as np
import rasterio
from rasterio.transform import from_bounds
import xarray as xr
import pyproj

from .logging_utils import LoggingUtils


class GridUtils:
    """
    Utility functions for spatial grid operations.
    
    This class provides static methods for common grid operations including
    coordinate creation, grid definition parsing, and spatial calculations.
    
    Example:
        >>> lons, lats = GridUtils.create_coordinate_arrays(
        ...     (-125.0, 25.0, -67.0, 49.0), (896, 1152)
        ... )
        >>> grid_info = GridUtils.parse_grid_definition("./config/grid_4kmCONUS.txt")
    """
    
    @staticmethod
    def create_coordinate_arrays(
        bounds: Tuple[float, float, float, float],
        shape: Tuple[int, int]
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Create longitude and latitude coordinate arrays from bounds and shape.
        
        Args:
            bounds: (west, south, east, north) bounds in degrees
            shape: (height, width) of the grid
            
        Returns:
            Tuple of (longitude, latitude) 1D arrays
        """
        west, south, east, north = bounds
        height, width = shape
        
        # Create coordinate arrays
        lons = np.linspace(west, east, width)
        lats = np.linspace(north, south, height)  # North to south for image orientation
        
        return lons, lats
    
    @staticmethod
    def parse_grid_definition(grid_file: str) -> Dict[str, Any]:
        """
        Parse a CDO-style grid definition file.
        
        Args:
            grid_file: Path to grid definition file
            
        Returns:
            Dictionary with grid parameters
        """
        logger = LoggingUtils.get_logger(__name__)
        grid_info = {}
        
        try:
            with open(grid_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        # Parse key = value pairs
                        if '=' in line:
                            key, value = line.split('=', 1)
                            key = key.strip()
                            value = value.strip()
                            
                            # Remove comments
                            if '#' in value:
                                value = value.split('#')[0].strip()
                            
                            # Convert numeric values
                            try:
                                if '.' in value:
                                    grid_info[key] = float(value)
                                else:
                                    grid_info[key] = int(value)
                            except ValueError:
                                grid_info[key] = value
            
            logger.debug(f"Parsed grid definition from {grid_file}")
            
        except Exception as e:
            logger.error(f"Error parsing grid definition {grid_file}: {e}")
            raise
        
        return grid_info
    
    @staticmethod
    def create_grid_from_definition(grid_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create grid arrays from grid definition parameters.
        
        Args:
            grid_info: Grid definition dictionary
            
        Returns:
            Dictionary with grid arrays and metadata
        """
        # Extract grid parameters
        xsize = grid_info.get('xsize', grid_info.get('nx'))
        ysize = grid_info.get('ysize', grid_info.get('ny'))
        xfirst = grid_info.get('xfirst', grid_info.get('west'))
        yfirst = grid_info.get('yfirst', grid_info.get('south'))
        xinc = grid_info.get('xinc', grid_info.get('dx'))
        yinc = grid_info.get('yinc', grid_info.get('dy'))
        
        if None in [xsize, ysize, xfirst, yfirst, xinc, yinc]:
            raise ValueError("Missing required grid parameters")
        
        # Create coordinate arrays
        lons = np.linspace(xfirst, xfirst + (xsize - 1) * xinc, xsize)
        lats = np.linspace(yfirst, yfirst + (ysize - 1) * yinc, ysize)
        
        # Calculate bounds
        west = float(np.min(lons))
        east = float(np.max(lons))
        south = float(np.min(lats))
        north = float(np.max(lats))
        
        return {
            'lons': lons,
            'lats': lats,
            'bounds': (west, south, east, north),
            'shape': (ysize, xsize),
            'resolution': (xinc, yinc)
        }
    
    @staticmethod
    def calculate_grid_bounds(
        center_lon: float,
        center_lat: float,
        width_km: float,
        height_km: float
    ) -> Tuple[float, float, float, float]:
        """
        Calculate grid bounds from center point and dimensions.
        
        Args:
            center_lon: Center longitude in degrees
            center_lat: Center latitude in degrees
            width_km: Width in kilometers
            height_km: Height in kilometers
            
        Returns:
            Tuple of (west, south, east, north) bounds
        """
        # Approximate conversion from km to degrees
        # 1 degree latitude ≈ 111 km
        # 1 degree longitude ≈ 111 km * cos(latitude)
        
        lat_deg_per_km = 1.0 / 111.0
        lon_deg_per_km = 1.0 / (111.0 * np.cos(np.radians(center_lat)))
        
        half_width_deg = (width_km / 2.0) * lon_deg_per_km
        half_height_deg = (height_km / 2.0) * lat_deg_per_km
        
        west = center_lon - half_width_deg
        east = center_lon + half_width_deg
        south = center_lat - half_height_deg
        north = center_lat + half_height_deg
        
        return west, south, east, north
    
    @staticmethod
    def calculate_grid_resolution_km(
        bounds: Tuple[float, float, float, float],
        shape: Tuple[int, int],
        center_lat: Optional[float] = None
    ) -> Tuple[float, float]:
        """
        Calculate grid resolution in kilometers.
        
        Args:
            bounds: (west, south, east, north) bounds in degrees
            shape: (height, width) of the grid
            center_lat: Center latitude for longitude correction. If None, uses bounds center.
            
        Returns:
            Tuple of (x_resolution_km, y_resolution_km)
        """
        west, south, east, north = bounds
        height, width = shape
        
        if center_lat is None:
            center_lat = (south + north) / 2.0
        
        # Calculate resolutions in degrees
        x_res_deg = (east - west) / width
        y_res_deg = (north - south) / height
        
        # Convert to kilometers
        # 1 degree latitude ≈ 111 km
        y_res_km = y_res_deg * 111.0
        
        # 1 degree longitude ≈ 111 km * cos(latitude)
        x_res_km = x_res_deg * 111.0 * np.cos(np.radians(center_lat))
        
        return x_res_km, y_res_km
    
    @staticmethod
    def create_affine_transform(
        bounds: Tuple[float, float, float, float],
        shape: Tuple[int, int]
    ) -> rasterio.Affine:
        """
        Create an affine transformation matrix from bounds and shape.
        
        Args:
            bounds: (west, south, east, north) bounds
            shape: (height, width) of the grid
            
        Returns:
            Affine transformation matrix
        """
        west, south, east, north = bounds
        height, width = shape
        
        return from_bounds(west, south, east, north, width, height)
    
    @staticmethod
    def grid_contains_point(
        point_lon: float,
        point_lat: float,
        bounds: Tuple[float, float, float, float]
    ) -> bool:
        """
        Check if a point is contained within grid bounds.
        
        Args:
            point_lon: Point longitude
            point_lat: Point latitude
            bounds: (west, south, east, north) grid bounds
            
        Returns:
            True if point is within bounds
        """
        west, south, east, north = bounds
        
        return (west <= point_lon <= east) and (south <= point_lat <= north)
    
    @staticmethod
    def find_grid_indices(
        point_lon: float,
        point_lat: float,
        lons: np.ndarray,
        lats: np.ndarray
    ) -> Tuple[int, int]:
        """
        Find grid indices for a given point.
        
        Args:
            point_lon: Point longitude
            point_lat: Point latitude
            lons: Longitude coordinate array
            lats: Latitude coordinate array
            
        Returns:
            Tuple of (lat_index, lon_index)
        """
        # Find nearest indices
        lon_idx = np.argmin(np.abs(lons - point_lon))
        lat_idx = np.argmin(np.abs(lats - point_lat))
        
        return lat_idx, lon_idx
    
    @staticmethod
    def calculate_grid_area(
        bounds: Tuple[float, float, float, float],
        shape: Tuple[int, int]
    ) -> np.ndarray:
        """
        Calculate area of each grid cell in square kilometers.
        
        Args:
            bounds: (west, south, east, north) bounds in degrees
            shape: (height, width) of the grid
            
        Returns:
            2D array of grid cell areas in km²
        """
        west, south, east, north = bounds
        height, width = shape
        
        # Create coordinate arrays
        lons = np.linspace(west, east, width)
        lats = np.linspace(north, south, height)
        
        # Calculate cell dimensions
        dlon = (east - west) / width
        dlat = (north - south) / height
        
        # Create 2D latitude array for area calculation
        lat_2d = np.broadcast_to(lats[:, np.newaxis], (height, width))
        
        # Calculate area for each cell
        # Area = R² * dlon * dlat * cos(lat)
        # where R is Earth's radius (≈ 6371 km)
        R = 6371.0  # Earth's radius in km
        dlon_rad = np.radians(dlon)
        dlat_rad = np.radians(dlat)
        
        area = R**2 * dlon_rad * dlat_rad * np.cos(np.radians(lat_2d))
        
        return area
    
    @staticmethod
    def regrid_conservative(
        source_data: np.ndarray,
        source_bounds: Tuple[float, float, float, float],
        target_bounds: Tuple[float, float, float, float],
        target_shape: Tuple[int, int]
    ) -> np.ndarray:
        """
        Perform conservative regridding (area-weighted interpolation).
        
        Args:
            source_data: Source data array
            source_bounds: Source grid bounds
            target_bounds: Target grid bounds
            target_shape: Target grid shape
            
        Returns:
            Regridded data array
        """
        # This is a simplified conservative regridding
        # For production use, consider using xESMF or similar libraries
        
        # Create coordinate arrays for source and target
        source_lons, source_lats = GridUtils.create_coordinate_arrays(
            source_bounds, source_data.shape
        )
        target_lons, target_lats = GridUtils.create_coordinate_arrays(
            target_bounds, target_shape
        )
        
        # Create xarray datasets for interpolation
        source_ds = xr.DataArray(
            source_data,
            coords={'lat': source_lats, 'lon': source_lons},
            dims=['lat', 'lon']
        )
        
        # Interpolate to target grid
        target_data = source_ds.interp(
            lat=target_lats,
            lon=target_lons,
            method='linear'
        ).values
        
        return target_data
    
    @staticmethod
    def create_grid_mask(
        bounds: Tuple[float, float, float, float],
        shape: Tuple[int, int],
        mask_bounds: Tuple[float, float, float, float]
    ) -> np.ndarray:
        """
        Create a boolean mask for a subset of the grid.
        
        Args:
            bounds: Full grid bounds
            shape: Full grid shape
            mask_bounds: Bounds for the mask region
            
        Returns:
            Boolean mask array (True for masked region)
        """
        lons, lats = GridUtils.create_coordinate_arrays(bounds, shape)
        mask_west, mask_south, mask_east, mask_north = mask_bounds
        
        # Create 2D coordinate grids
        lon_grid, lat_grid = np.meshgrid(lons, lats)
        
        # Create mask
        mask = (
            (lon_grid >= mask_west) & (lon_grid <= mask_east) &
            (lat_grid >= mask_south) & (lat_grid <= mask_north)
        )
        
        return mask
    
    @staticmethod
    def validate_grid_consistency(
        grid1_bounds: Tuple[float, float, float, float],
        grid1_shape: Tuple[int, int],
        grid2_bounds: Tuple[float, float, float, float],
        grid2_shape: Tuple[int, int],
        tolerance: float = 1e-6
    ) -> bool:
        """
        Check if two grids are consistent (same bounds and resolution).
        
        Args:
            grid1_bounds: First grid bounds
            grid1_shape: First grid shape
            grid2_bounds: Second grid bounds
            grid2_shape: Second grid shape
            tolerance: Tolerance for floating point comparison
            
        Returns:
            True if grids are consistent
        """
        # Check shapes
        if grid1_shape != grid2_shape:
            return False
        
        # Check bounds
        bounds_diff = np.array(grid1_bounds) - np.array(grid2_bounds)
        if np.any(np.abs(bounds_diff) > tolerance):
            return False
        
        return True
