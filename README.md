# PRISM Hourly Dataset Creation Package

A professional Python package for creating hourly precipitation estimates by combining daily PRISM precipitation data with hourly NCEP Stage IV radar patterns through temporal disaggregation.

## Overview

This package implements a temporal disaggregation methodology that:
- Uses PRISM daily precipitation totals as the spatial "truth" for high accuracy
- Uses NCEP Stage IV radar data to provide hourly temporal patterns
- Ensures mass conservation (hourly estimates sum exactly to daily totals)
- Provides comprehensive quality control and validation
- Supports large-scale processing with parallel computing

## Features

- **Data Download**: Automated downloading of PRISM daily and NCEP Stage IV data
- **Data Processing**: Reprojection and format conversion to common grids
- **Temporal Disaggregation**: Core algorithm for creating hourly estimates
- **Quality Control**: Comprehensive validation and error checking
- **Command Line Interface**: Easy-to-use CLI for all operations
- **Configuration Management**: Flexible YAML-based configuration system
- **Logging and Monitoring**: Detailed logging and progress tracking

## Installation

### From Source

```bash
# Clone the repository
git clone <repository-url>
cd create_prism_hourly_dataset

# Install in development mode
pip install -e .

# Or install normally
pip install .
```

### Dependencies

The package requires Python 3.8+ and the following dependencies:
- click (CLI interface)
- numpy, pandas (data manipulation)
- xarray, netcdf4 (NetCDF file handling)
- rasterio, pyproj (spatial data processing)
- requests (data downloading)
- pyyaml (configuration management)
- scipy (scientific computing)
- tqdm (progress bars)
- matplotlib (plotting, optional)

## Quick Start

### 1. Configuration

Create a configuration file or use the default settings:

```bash
# View current configuration
prism-hourly config

# Copy default config for customization
cp config/config.yaml my_config.yaml
```

### 2. Download Data

```bash
# Download PRISM daily data for 2023
prism-hourly download prism --start-date 2023-01-01 --end-date 2023-12-31

# Download NCEP Stage IV data for 2023
prism-hourly download ncep --start-year 2023 --end-year 2023 --extract
```

### 3. Process Data

```bash
# Process PRISM files to common grid
prism-hourly process prism --input-dir ./data/prism_raw --output-dir ./data/prism_processed

# Process NCEP files (if needed)
# Note: NCEP processing is typically handled during disaggregation
```

### 4. Create Hourly Estimates

```bash
# Perform temporal disaggregation
prism-hourly disaggregate \
    --prism-dir ./data/prism_processed \
    --ncep-dir ./data/ncep_processed \
    --output-dir ./data/prism_hourly \
    --hour-start 12 \
    --hour-end 36
```

### 5. Validate Results

```bash
# Validate a dataset
prism-hourly validate dataset ./data/prism_hourly/PRISM_2023010112.nc

# Check mass conservation
prism-hourly validate conservation \
    --daily-file ./data/prism_processed/PRISM_20230101.nc \
    --hourly-dir ./data/prism_hourly
```

## Usage Examples

### Python API

```python
from prism_hourly import (
    PRISMDownloader, NCEPDownloader,
    DataProcessor, TemporalDisaggregator,
    ConfigManager
)
from datetime import date

# Load configuration
config_manager = ConfigManager("config/config.yaml")
config = config_manager.get_config()

# Download PRISM data
downloader = PRISMDownloader(output_dir="./data/prism_raw")
files = downloader.download_date_range(
    date(2023, 1, 1),
    date(2023, 1, 31)
)

# Process data
processor = DataProcessor(
    target_grid_file="./config/Terrain.tif",
    resolution=4000.0
)
processed_files = processor.process_prism_files(
    "./data/prism_raw",
    "./data/prism_processed"
)

# Create hourly estimates
disaggregator = TemporalDisaggregator(
    prism_dir="./data/prism_processed",
    ncep_dir="./data/ncep_processed",
    output_dir="./data/prism_hourly",
    hour_range=(12, 36)
)
stats = disaggregator.process_all_prism_files()
```

### Jupyter Notebook Examples

See the `examples/` directory for detailed Jupyter notebook examples:
- `01_basic_usage.ipynb`: Basic package usage
- `02_advanced_processing.ipynb`: Advanced processing options
- `03_quality_control.ipynb`: Quality control and validation
- `04_visualization.ipynb`: Data visualization examples

## Configuration

The package uses a hierarchical YAML configuration system. Key configuration sections:

### Download Configuration
```yaml
download:
  prism_base_url: "https://data.prism.oregonstate.edu/daily/ppt"
  prism_output_dir: "./data/prism_raw"
  ncep_base_url: "https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4"
  ncep_output_dir: "./data/ncep_raw"
  download_delay: 2.0
  max_retries: 3
  timeout: 300
```

### Processing Configuration
```yaml
processing:
  target_grid_file: "./config/Terrain.tif"
  resolution: 4000.0
  resampling_method: "cubic"
  prism_processed_dir: "./data/prism_processed"
  ncep_processed_dir: "./data/ncep_processed"
  nodata_value: -9999.0
  compress_output: true
```

### Disaggregation Configuration
```yaml
disaggregation:
  hour_range_start: 12
  hour_range_end: 36
  output_dir: "./data/prism_hourly"
  quality_control: true
  mass_conservation_check: true
  tolerance: 0.01
  fill_missing_hours: true
```

## Data Sources

### PRISM Data
- **Source**: PRISM Climate Group, Oregon State University
- **URL**: https://data.prism.oregonstate.edu/daily/ppt
- **Resolution**: 4km daily precipitation
- **Coverage**: Continental United States
- **Format**: BIL files in ZIP archives

### NCEP Stage IV Data
- **Source**: NCEP/NWS Stage IV precipitation analysis
- **URL**: https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4
- **Resolution**: 4km hourly precipitation
- **Coverage**: Continental United States
- **Format**: GRIB files in TAR archives

## Methodology

The temporal disaggregation algorithm:

1. **Load Daily Data**: Read PRISM daily precipitation totals
2. **Load Hourly Patterns**: Read NCEP Stage IV hourly data for the same day
3. **Calculate Ratios**: Compute hourly ratios from NCEP data
4. **Apply Disaggregation**: Multiply PRISM daily totals by hourly ratios
5. **Quality Control**: Ensure mass conservation and data quality
6. **Output**: Save hourly estimates in NetCDF format

### Mass Conservation

The algorithm ensures that hourly estimates sum exactly to the original PRISM daily totals:

```
∑(hourly_estimates) = PRISM_daily_total
```

This is achieved by:
1. Calculating the ratio of each hour's NCEP precipitation to the daily NCEP total
2. Multiplying the PRISM daily total by each hourly ratio
3. Validating that the sum equals the original daily total within tolerance

## Output Format

Hourly estimates are saved as NetCDF files with the following structure:

```
dimensions:
    time = 1
    lat = 896
    lon = 1152

variables:
    precipitation(time, lat, lon):
        units: "mm"
        long_name: "Hourly precipitation estimate"
        standard_name: "precipitation_amount"

    time(time):
        units: "hours since 1970-01-01 00:00:00"
        calendar: "gregorian"

    lat(lat):
        units: "degrees_north"
        long_name: "latitude"

    lon(lon):
        units: "degrees_east"
        long_name: "longitude"

global attributes:
    title: "PRISM Hourly Precipitation Estimate"
    source: "Temporal disaggregation of PRISM daily and NCEP Stage IV hourly data"
    method: "Temporal disaggregation preserving daily mass"
    grid_resolution: "4000m"
```

## Quality Control

The package includes comprehensive quality control:

### Spatial Consistency
- Coordinate validation
- Grid alignment checks
- Spatial bounds verification

### Temporal Consistency
- Time coordinate validation
- Temporal ordering checks
- Date range verification

### Data Quality
- Value range checks (non-negative precipitation)
- Missing data handling
- Outlier detection

### Mass Conservation
- Hourly sums must equal daily totals within tolerance
- Relative error tracking
- Conservation statistics reporting

## Performance Considerations

### Memory Usage
- Process data in chunks for large datasets
- Use compression for output files
- Configure memory limits in settings

### Parallel Processing
- Enable multiprocessing for large-scale operations
- Configure worker processes based on system resources
- Use progress bars for long-running operations

### Storage Requirements
- Raw PRISM data: ~1GB per year
- Raw NCEP data: ~100GB per year
- Processed hourly estimates: ~50GB per year

## Troubleshooting

### Common Issues

1. **Download Failures**
   - Check internet connection
   - Verify URLs are accessible
   - Increase timeout settings
   - Check disk space

2. **Processing Errors**
   - Verify input file formats
   - Check grid definition files exist
   - Ensure sufficient memory
   - Validate coordinate systems

3. **Mass Conservation Violations**
   - Check for missing NCEP data
   - Verify temporal alignment
   - Adjust tolerance settings
   - Review data quality

### Debug Mode

Enable debug logging for detailed troubleshooting:

```bash
prism-hourly --log-level DEBUG --verbose <command>
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## Testing

Run the test suite:

```bash
# Install test dependencies
pip install -e ".[dev]"

# Run tests
pytest tests/

# Run with coverage
pytest --cov=prism_hourly tests/
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Citation

If you use this package in your research, please cite:

```
[Your Name] (2024). PRISM Hourly Dataset Creation Package.
GitHub repository: https://github.com/[username]/create_prism_hourly_dataset
```

## Acknowledgments

- PRISM Climate Group at Oregon State University for daily precipitation data
- NCEP/NWS for Stage IV radar precipitation analysis
- The scientific community for temporal disaggregation methodologies

## Support

For questions, issues, or contributions:
- Open an issue on GitHub
- Check the documentation in `docs/`
- Review example notebooks in `examples/`

## Changelog

### Version 1.0.0
- Initial release
- Core temporal disaggregation functionality
- Command-line interface
- Comprehensive validation and quality control
- Configuration management system
- Documentation and examples
