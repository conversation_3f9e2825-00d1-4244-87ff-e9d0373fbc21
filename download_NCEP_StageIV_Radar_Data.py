#!/usr/bin/env python
""" 
Python script to download selected files from rda.ucar.edu.
After you save the file, don't forget to make it executable
i.e. - "chmod 755 <name_of_script>"
"""
import sys, os
from urllib.request import build_opener

opener = build_opener()

filelist = [
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202301.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202302.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202303.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202304.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202305.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202306.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202307.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202308.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202309.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202310.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202311.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202312.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202401.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202402.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202403.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202404.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202405.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202406.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202407.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202408.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202409.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202410.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202411.tar',
  'https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4/stage4.202412.tar'
]

for file in filelist:
    ofile = os.path.basename(file)
    sys.stdout.write("downloading " + ofile + " ... ")
    sys.stdout.flush()
    infile = opener.open(file)
    outfile = open(ofile, "wb")
    outfile.write(infile.read())
    outfile.close()
    sys.stdout.write("done\n")
