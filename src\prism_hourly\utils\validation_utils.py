"""
Validation utilities module.

This module provides comprehensive validation functions for data quality,
spatial consistency, temporal consistency, and file integrity checks.
"""

import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
import logging
from datetime import datetime, timedelta
import hashlib

import numpy as np
import xarray as xr
import pandas as pd
from tqdm import tqdm

from .logging_utils import LoggingUtils


class ValidationUtils:
    """
    Comprehensive validation utilities for the PRISM hourly dataset package.
    
    This class provides methods for validating data quality, spatial and temporal
    consistency, file integrity, and other quality control checks.
    
    Attributes:
        logger (logging.Logger): Logger instance for this class
    
    Example:
        >>> validator = ValidationUtils()
        >>> results = validator.validate_dataset("./data/prism_hourly/PRISM_20230101.nc")
        >>> if results['is_valid']:
        ...     print("Dataset is valid")
    """
    
    def __init__(self):
        """Initialize the validation utilities."""
        self.logger = LoggingUtils.get_logger(__name__)
    
    def validate_dataset(
        self,
        dataset_path: str,
        check_spatial: bool = True,
        check_temporal: bool = True,
        check_data_ranges: bool = True,
        check_metadata: bool = True
    ) -> Dict[str, Any]:
        """
        Perform comprehensive validation of a dataset.
        
        Args:
            dataset_path: Path to dataset file
            check_spatial: Whether to check spatial consistency
            check_temporal: Whether to check temporal consistency
            check_data_ranges: Whether to check data value ranges
            check_metadata: Whether to check metadata completeness
            
        Returns:
            Dictionary with validation results
        """
        results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'file_path': dataset_path,
            'checks_performed': []
        }
        
        try:
            # Check if file exists
            if not Path(dataset_path).exists():
                results['errors'].append(f"File does not exist: {dataset_path}")
                results['is_valid'] = False
                return results
            
            # Load dataset
            try:
                dataset = xr.open_dataset(dataset_path)
            except Exception as e:
                results['errors'].append(f"Cannot open dataset: {e}")
                results['is_valid'] = False
                return results
            
            # Perform checks
            if check_spatial:
                spatial_results = self._check_spatial_consistency(dataset)
                results['checks_performed'].append('spatial')
                results['spatial'] = spatial_results
                if not spatial_results['is_valid']:
                    results['is_valid'] = False
                    results['errors'].extend(spatial_results['errors'])
                    results['warnings'].extend(spatial_results['warnings'])
            
            if check_temporal:
                temporal_results = self._check_temporal_consistency(dataset)
                results['checks_performed'].append('temporal')
                results['temporal'] = temporal_results
                if not temporal_results['is_valid']:
                    results['is_valid'] = False
                    results['errors'].extend(temporal_results['errors'])
                    results['warnings'].extend(temporal_results['warnings'])
            
            if check_data_ranges:
                range_results = self._check_data_ranges(dataset)
                results['checks_performed'].append('data_ranges')
                results['data_ranges'] = range_results
                if not range_results['is_valid']:
                    results['is_valid'] = False
                    results['errors'].extend(range_results['errors'])
                    results['warnings'].extend(range_results['warnings'])
            
            if check_metadata:
                metadata_results = self._check_metadata_completeness(dataset)
                results['checks_performed'].append('metadata')
                results['metadata'] = metadata_results
                if not metadata_results['is_valid']:
                    results['warnings'].extend(metadata_results['warnings'])
                    # Metadata issues are warnings, not errors
            
            dataset.close()
            
        except Exception as e:
            results['errors'].append(f"Validation error: {e}")
            results['is_valid'] = False
        
        return results
    
    def _check_spatial_consistency(self, dataset: xr.Dataset) -> Dict[str, Any]:
        """Check spatial consistency of the dataset."""
        results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'grid_info': {}
        }
        
        try:
            # Check for required coordinates
            required_coords = ['lat', 'lon']
            for coord in required_coords:
                if coord not in dataset.coords:
                    results['errors'].append(f"Missing required coordinate: {coord}")
                    results['is_valid'] = False
            
            if not results['is_valid']:
                return results
            
            # Check coordinate properties
            lats = dataset.lat.values
            lons = dataset.lon.values
            
            # Check for monotonic coordinates
            if not np.all(np.diff(lats) < 0):  # Lats should be decreasing (north to south)
                results['warnings'].append("Latitude coordinates are not monotonically decreasing")
            
            if not np.all(np.diff(lons) > 0):  # Lons should be increasing (west to east)
                results['warnings'].append("Longitude coordinates are not monotonically increasing")
            
            # Check coordinate ranges
            if np.min(lats) < -90 or np.max(lats) > 90:
                results['errors'].append(f"Invalid latitude range: {np.min(lats)} to {np.max(lats)}")
                results['is_valid'] = False
            
            if np.min(lons) < -180 or np.max(lons) > 180:
                results['errors'].append(f"Invalid longitude range: {np.min(lons)} to {np.max(lons)}")
                results['is_valid'] = False
            
            # Store grid information
            results['grid_info'] = {
                'lat_range': (float(np.min(lats)), float(np.max(lats))),
                'lon_range': (float(np.min(lons)), float(np.max(lons))),
                'shape': (len(lats), len(lons)),
                'lat_resolution': float(np.mean(np.abs(np.diff(lats)))),
                'lon_resolution': float(np.mean(np.abs(np.diff(lons))))
            }
            
        except Exception as e:
            results['errors'].append(f"Spatial consistency check error: {e}")
            results['is_valid'] = False
        
        return results
    
    def _check_temporal_consistency(self, dataset: xr.Dataset) -> Dict[str, Any]:
        """Check temporal consistency of the dataset."""
        results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'time_info': {}
        }
        
        try:
            # Check for time coordinate
            if 'time' not in dataset.coords:
                results['errors'].append("Missing time coordinate")
                results['is_valid'] = False
                return results
            
            times = dataset.time.values
            
            # Check for valid time values
            if len(times) == 0:
                results['errors'].append("No time values found")
                results['is_valid'] = False
                return results
            
            # Convert to pandas datetime for easier handling
            time_index = pd.to_datetime(times)
            
            # Check for duplicate times
            if len(time_index) != len(time_index.unique()):
                results['warnings'].append("Duplicate time values found")
            
            # Check time ordering
            if not time_index.is_monotonic_increasing:
                results['warnings'].append("Time values are not monotonically increasing")
            
            # Store time information
            results['time_info'] = {
                'start_time': str(time_index.min()),
                'end_time': str(time_index.max()),
                'num_times': len(time_index),
                'time_range_days': (time_index.max() - time_index.min()).days
            }
            
            # Check for reasonable time range
            if time_index.min().year < 1900 or time_index.max().year > 2100:
                results['warnings'].append(f"Unusual time range: {time_index.min()} to {time_index.max()}")
            
        except Exception as e:
            results['errors'].append(f"Temporal consistency check error: {e}")
            results['is_valid'] = False
        
        return results
    
    def _check_data_ranges(
        self,
        dataset: xr.Dataset,
        min_precip: float = 0.0,
        max_precip: float = 500.0
    ) -> Dict[str, Any]:
        """Check data value ranges for reasonableness."""
        results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'data_stats': {}
        }
        
        try:
            # Check precipitation variable
            if 'precipitation' not in dataset.data_vars:
                results['errors'].append("Missing precipitation variable")
                results['is_valid'] = False
                return results
            
            precip_data = dataset['precipitation'].values
            
            # Remove fill values and NaNs for statistics
            valid_data = precip_data[~np.isnan(precip_data)]
            if hasattr(dataset['precipitation'], '_FillValue'):
                fill_value = dataset['precipitation']._FillValue
                valid_data = valid_data[valid_data != fill_value]
            
            if len(valid_data) == 0:
                results['warnings'].append("No valid precipitation data found")
                return results
            
            # Calculate statistics
            data_min = float(np.min(valid_data))
            data_max = float(np.max(valid_data))
            data_mean = float(np.mean(valid_data))
            data_std = float(np.std(valid_data))
            
            results['data_stats'] = {
                'min': data_min,
                'max': data_max,
                'mean': data_mean,
                'std': data_std,
                'valid_points': len(valid_data),
                'total_points': precip_data.size,
                'fill_ratio': 1.0 - (len(valid_data) / precip_data.size)
            }
            
            # Check for negative precipitation
            if data_min < min_precip:
                results['errors'].append(f"Negative precipitation values found: min = {data_min}")
                results['is_valid'] = False
            
            # Check for unreasonably high precipitation
            if data_max > max_precip:
                results['warnings'].append(f"Very high precipitation values found: max = {data_max}")
            
            # Check for excessive fill values
            if results['data_stats']['fill_ratio'] > 0.5:
                results['warnings'].append(f"High fill value ratio: {results['data_stats']['fill_ratio']:.2%}")
            
        except Exception as e:
            results['errors'].append(f"Data range check error: {e}")
            results['is_valid'] = False
        
        return results
    
    def _check_metadata_completeness(self, dataset: xr.Dataset) -> Dict[str, Any]:
        """Check metadata completeness."""
        results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'metadata_info': {}
        }
        
        try:
            # Required global attributes
            required_attrs = ['title', 'source', 'creation_date']
            missing_attrs = []
            
            for attr in required_attrs:
                if attr not in dataset.attrs:
                    missing_attrs.append(attr)
            
            if missing_attrs:
                results['warnings'].append(f"Missing global attributes: {missing_attrs}")
            
            # Check variable attributes
            if 'precipitation' in dataset.data_vars:
                precip_var = dataset['precipitation']
                required_var_attrs = ['units', 'long_name']
                missing_var_attrs = []
                
                for attr in required_var_attrs:
                    if attr not in precip_var.attrs:
                        missing_var_attrs.append(attr)
                
                if missing_var_attrs:
                    results['warnings'].append(f"Missing precipitation attributes: {missing_var_attrs}")
            
            # Store metadata information
            results['metadata_info'] = {
                'global_attrs': list(dataset.attrs.keys()),
                'variables': list(dataset.data_vars.keys()),
                'coordinates': list(dataset.coords.keys())
            }
            
        except Exception as e:
            results['warnings'].append(f"Metadata check error: {e}")
        
        return results
    
    def validate_file_integrity(self, file_path: str) -> Dict[str, Any]:
        """
        Validate file integrity using checksums and basic file checks.
        
        Args:
            file_path: Path to file to validate
            
        Returns:
            Dictionary with integrity check results
        """
        results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'file_info': {}
        }
        
        try:
            file_path = Path(file_path)
            
            # Check if file exists
            if not file_path.exists():
                results['errors'].append(f"File does not exist: {file_path}")
                results['is_valid'] = False
                return results
            
            # Get file information
            stat = file_path.stat()
            results['file_info'] = {
                'size_bytes': stat.st_size,
                'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'is_readable': os.access(file_path, os.R_OK)
            }
            
            # Check if file is empty
            if stat.st_size == 0:
                results['errors'].append("File is empty")
                results['is_valid'] = False
                return results
            
            # Check if file is readable
            if not results['file_info']['is_readable']:
                results['errors'].append("File is not readable")
                results['is_valid'] = False
                return results
            
            # Calculate MD5 checksum
            try:
                md5_hash = hashlib.md5()
                with open(file_path, 'rb') as f:
                    for chunk in iter(lambda: f.read(4096), b""):
                        md5_hash.update(chunk)
                results['file_info']['md5_checksum'] = md5_hash.hexdigest()
            except Exception as e:
                results['warnings'].append(f"Could not calculate checksum: {e}")
            
        except Exception as e:
            results['errors'].append(f"File integrity check error: {e}")
            results['is_valid'] = False
        
        return results
    
    def validate_mass_conservation(
        self,
        daily_file: str,
        hourly_files: List[str],
        tolerance: float = 0.01
    ) -> Dict[str, Any]:
        """
        Validate mass conservation between daily and hourly data.
        
        Args:
            daily_file: Path to daily precipitation file
            hourly_files: List of paths to hourly precipitation files
            tolerance: Tolerance for mass conservation check (fraction)
            
        Returns:
            Dictionary with mass conservation results
        """
        results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'conservation_stats': {}
        }
        
        try:
            # Load daily data
            daily_ds = xr.open_dataset(daily_file)
            daily_total = daily_ds['precipitation'].values
            
            # Load and sum hourly data
            hourly_sum = None
            valid_hourly_files = []
            
            for hourly_file in hourly_files:
                if Path(hourly_file).exists():
                    try:
                        hourly_ds = xr.open_dataset(hourly_file)
                        hourly_data = hourly_ds['precipitation'].values
                        
                        if hourly_sum is None:
                            hourly_sum = hourly_data.copy()
                        else:
                            hourly_sum += hourly_data
                        
                        valid_hourly_files.append(hourly_file)
                        hourly_ds.close()
                        
                    except Exception as e:
                        results['warnings'].append(f"Could not load {hourly_file}: {e}")
            
            if hourly_sum is None:
                results['errors'].append("No valid hourly files found")
                results['is_valid'] = False
                return results
            
            # Calculate conservation statistics
            # Mask for non-zero daily values
            mask = (daily_total > 0) & (~np.isnan(daily_total))
            
            if np.any(mask):
                daily_masked = daily_total[mask]
                hourly_masked = hourly_sum[mask]
                
                # Calculate relative differences
                rel_diff = np.abs((hourly_masked - daily_masked) / daily_masked)
                
                max_error = float(np.max(rel_diff))
                mean_error = float(np.mean(rel_diff))
                
                results['conservation_stats'] = {
                    'max_relative_error': max_error,
                    'mean_relative_error': mean_error,
                    'tolerance': tolerance,
                    'points_checked': int(np.sum(mask)),
                    'valid_hourly_files': len(valid_hourly_files),
                    'total_hourly_files': len(hourly_files)
                }
                
                # Check if within tolerance
                if max_error > tolerance:
                    results['errors'].append(
                        f"Mass conservation violated: max error {max_error:.4f} > tolerance {tolerance}"
                    )
                    results['is_valid'] = False
                
                if mean_error > tolerance / 2:
                    results['warnings'].append(
                        f"High mean conservation error: {mean_error:.4f}"
                    )
            
            daily_ds.close()
            
        except Exception as e:
            results['errors'].append(f"Mass conservation check error: {e}")
            results['is_valid'] = False
        
        return results
    
    def validate_directory_structure(self, base_dir: str) -> Dict[str, Any]:
        """
        Validate directory structure and file organization.
        
        Args:
            base_dir: Base directory to validate
            
        Returns:
            Dictionary with directory validation results
        """
        results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'structure_info': {}
        }
        
        try:
            base_path = Path(base_dir)
            
            if not base_path.exists():
                results['errors'].append(f"Base directory does not exist: {base_dir}")
                results['is_valid'] = False
                return results
            
            # Count files by type
            file_counts = {}
            total_size = 0
            
            for file_path in base_path.rglob('*'):
                if file_path.is_file():
                    suffix = file_path.suffix.lower()
                    file_counts[suffix] = file_counts.get(suffix, 0) + 1
                    total_size += file_path.stat().st_size
            
            results['structure_info'] = {
                'total_files': sum(file_counts.values()),
                'file_types': file_counts,
                'total_size_mb': total_size / (1024 * 1024),
                'directory_exists': True
            }
            
            # Check for expected file types
            expected_types = ['.nc', '.zip', '.tar']
            found_types = set(file_counts.keys())
            
            if not any(ext in found_types for ext in expected_types):
                results['warnings'].append("No expected data files found (.nc, .zip, .tar)")
            
        except Exception as e:
            results['errors'].append(f"Directory structure check error: {e}")
            results['is_valid'] = False
        
        return results
