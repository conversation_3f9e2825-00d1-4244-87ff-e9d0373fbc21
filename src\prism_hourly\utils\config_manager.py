"""
Configuration management module.

This module provides functionality for loading, validating, and managing
configuration settings for the PRISM hourly dataset package.
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional, Union
import logging
import yaml
from dataclasses import dataclass, field
from copy import deepcopy

from .logging_utils import LoggingUtils


@dataclass
class DownloadConfig:
    """Configuration for data download operations."""
    prism_base_url: str = "https://data.prism.oregonstate.edu/daily/ppt"
    prism_output_dir: str = "./data/prism_raw"
    ncep_base_url: str = "https://data-osdf.rda.ucar.edu/ncar/rda/d507005/stage4"
    ncep_output_dir: str = "./data/ncep_raw"
    download_delay: float = 2.0
    max_retries: int = 3
    timeout: int = 300
    verify_downloads: bool = True


@dataclass
class ProcessingConfig:
    """Configuration for data processing operations."""
    target_grid_file: str = "./config/Terrain.tif"
    grid_definition_file: str = "./config/grid_4kmCONUS.txt"
    resolution: float = 4000.0
    resampling_method: str = "cubic"
    prism_processed_dir: str = "./data/prism_processed"
    ncep_processed_dir: str = "./data/ncep_processed"
    nodata_value: float = -9999.0
    compress_output: bool = True
    chunk_size: int = 1000


@dataclass
class DisaggregationConfig:
    """Configuration for temporal disaggregation operations."""
    hour_range_start: int = 12
    hour_range_end: int = 36
    output_dir: str = "./data/prism_hourly"
    output_format: str = "netcdf4"
    quality_control: bool = True
    mass_conservation_check: bool = True
    tolerance: float = 0.01
    fill_missing_hours: bool = True
    interpolate_gaps: bool = False


@dataclass
class LoggingConfig:
    """Configuration for logging operations."""
    level: str = "INFO"
    file_path: str = "./logs/prism_hourly.log"
    max_file_size: str = "10MB"
    backup_count: int = 5
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    console_output: bool = True


@dataclass
class ValidationConfig:
    """Configuration for validation operations."""
    check_spatial_consistency: bool = True
    check_grid_alignment: bool = True
    check_temporal_consistency: bool = True
    check_time_coverage: bool = True
    check_data_ranges: bool = True
    min_precipitation: float = 0.0
    max_precipitation: float = 500.0
    check_file_integrity: bool = True
    verify_checksums: bool = False


@dataclass
class PerformanceConfig:
    """Configuration for performance settings."""
    use_multiprocessing: bool = True
    max_workers: int = 4
    memory_limit: str = "8GB"
    chunk_processing: bool = True
    enable_caching: bool = True
    cache_dir: str = "./cache"
    cache_size_limit: str = "1GB"


@dataclass
class AdvancedConfig:
    """Configuration for advanced settings."""
    source_crs: str = "EPSG:4326"
    target_crs: str = "EPSG:4326"
    preferred_format: str = "netcdf4"
    compression_level: int = 6
    continue_on_error: bool = False
    error_log_detail: bool = True


@dataclass
class Config:
    """Main configuration class containing all configuration sections."""
    download: DownloadConfig = field(default_factory=DownloadConfig)
    processing: ProcessingConfig = field(default_factory=ProcessingConfig)
    disaggregation: DisaggregationConfig = field(default_factory=DisaggregationConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    validation: ValidationConfig = field(default_factory=ValidationConfig)
    performance: PerformanceConfig = field(default_factory=PerformanceConfig)
    advanced: AdvancedConfig = field(default_factory=AdvancedConfig)


class ConfigManager:
    """
    Manage configuration settings for the PRISM hourly dataset package.
    
    This class provides a hierarchical configuration system that loads settings from:
    1. Default configuration (built-in)
    2. Configuration file (YAML)
    3. Environment variables (override specific settings)
    4. Command-line options (highest priority, handled externally)
    
    Attributes:
        config (Config): Current configuration object
        config_file (Optional[Path]): Path to configuration file
        logger (logging.Logger): Logger instance for this class
    
    Example:
        >>> config_manager = ConfigManager("config/config.yaml")
        >>> config = config_manager.get_config()
        >>> print(config.download.prism_base_url)
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize the configuration manager.
        
        Args:
            config_file: Path to YAML configuration file. If None, uses default config.
        """
        self.config_file = Path(config_file) if config_file else None
        self.logger = LoggingUtils.get_logger(__name__)
        
        # Load configuration
        self.config = self._load_config()
        
        # Apply environment variable overrides
        self._apply_env_overrides()
        
        self.logger.info("Configuration loaded successfully")
    
    def _load_config(self) -> Config:
        """Load configuration from file or use defaults."""
        # Start with default configuration
        config = Config()
        
        # Load from file if specified
        if self.config_file and self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    file_config = yaml.safe_load(f)
                
                # Update configuration with file values
                config = self._update_config_from_dict(config, file_config)
                self.logger.info(f"Loaded configuration from {self.config_file}")
                
            except Exception as e:
                self.logger.error(f"Error loading config file {self.config_file}: {e}")
                self.logger.info("Using default configuration")
        
        elif self.config_file:
            self.logger.warning(f"Config file {self.config_file} not found, using defaults")
        
        return config
    
    def _update_config_from_dict(self, config: Config, config_dict: Dict[str, Any]) -> Config:
        """Update configuration object from dictionary."""
        # Create a copy to avoid modifying the original
        updated_config = deepcopy(config)
        
        # Update each section
        for section_name, section_data in config_dict.items():
            if hasattr(updated_config, section_name) and isinstance(section_data, dict):
                section_obj = getattr(updated_config, section_name)
                
                for key, value in section_data.items():
                    if hasattr(section_obj, key):
                        setattr(section_obj, key, value)
                    else:
                        self.logger.warning(f"Unknown config key: {section_name}.{key}")
            else:
                self.logger.warning(f"Unknown config section: {section_name}")
        
        return updated_config
    
    def _apply_env_overrides(self):
        """Apply environment variable overrides to configuration."""
        env_prefix = "PRISM_HOURLY_"
        
        # Define environment variable mappings
        env_mappings = {
            f"{env_prefix}OUTPUT_DIR": ("disaggregation", "output_dir"),
            f"{env_prefix}LOG_LEVEL": ("logging", "level"),
            f"{env_prefix}RESOLUTION": ("processing", "resolution"),
            f"{env_prefix}MAX_WORKERS": ("performance", "max_workers"),
            f"{env_prefix}PRISM_DIR": ("download", "prism_output_dir"),
            f"{env_prefix}NCEP_DIR": ("download", "ncep_output_dir"),
            f"{env_prefix}DOWNLOAD_DELAY": ("download", "download_delay"),
            f"{env_prefix}QUALITY_CONTROL": ("disaggregation", "quality_control"),
        }
        
        for env_var, (section, key) in env_mappings.items():
            if env_var in os.environ:
                value = os.environ[env_var]
                
                # Convert value to appropriate type
                section_obj = getattr(self.config, section)
                current_value = getattr(section_obj, key)
                
                try:
                    if isinstance(current_value, bool):
                        value = value.lower() in ('true', '1', 'yes', 'on')
                    elif isinstance(current_value, int):
                        value = int(value)
                    elif isinstance(current_value, float):
                        value = float(value)
                    # str values don't need conversion
                    
                    setattr(section_obj, key, value)
                    self.logger.debug(f"Applied env override: {env_var} = {value}")
                    
                except ValueError as e:
                    self.logger.error(f"Invalid value for {env_var}: {value} ({e})")
    
    def get_config(self) -> Config:
        """
        Get the current configuration.
        
        Returns:
            Current configuration object
        """
        return self.config
    
    def update_config(self, updates: Dict[str, Any]):
        """
        Update configuration with new values.
        
        Args:
            updates: Dictionary of configuration updates
        """
        self.config = self._update_config_from_dict(self.config, updates)
        self.logger.info("Configuration updated")
    
    def save_config(self, output_path: str):
        """
        Save current configuration to a YAML file.
        
        Args:
            output_path: Path to save configuration file
        """
        config_dict = self._config_to_dict(self.config)
        
        with open(output_path, 'w') as f:
            yaml.dump(config_dict, f, default_flow_style=False, indent=2)
        
        self.logger.info(f"Configuration saved to {output_path}")
    
    def _config_to_dict(self, config: Config) -> Dict[str, Any]:
        """Convert configuration object to dictionary."""
        config_dict = {}
        
        for section_name in ['download', 'processing', 'disaggregation', 'logging', 
                           'validation', 'performance', 'advanced']:
            section_obj = getattr(config, section_name)
            config_dict[section_name] = {}
            
            for field_name in section_obj.__dataclass_fields__:
                value = getattr(section_obj, field_name)
                config_dict[section_name][field_name] = value
        
        return config_dict
    
    def validate_config(self) -> bool:
        """
        Validate the current configuration.
        
        Returns:
            True if configuration is valid
        """
        is_valid = True
        
        # Validate paths exist or can be created
        paths_to_check = [
            self.config.download.prism_output_dir,
            self.config.download.ncep_output_dir,
            self.config.processing.prism_processed_dir,
            self.config.processing.ncep_processed_dir,
            self.config.disaggregation.output_dir,
        ]
        
        for path_str in paths_to_check:
            path = Path(path_str)
            try:
                path.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                self.logger.error(f"Cannot create directory {path}: {e}")
                is_valid = False
        
        # Validate numeric ranges
        if not (0 <= self.config.disaggregation.tolerance <= 1):
            self.logger.error("Disaggregation tolerance must be between 0 and 1")
            is_valid = False
        
        if not (0 < self.config.download.download_delay <= 60):
            self.logger.error("Download delay must be between 0 and 60 seconds")
            is_valid = False
        
        if not (1 <= self.config.performance.max_workers <= 32):
            self.logger.error("Max workers must be between 1 and 32")
            is_valid = False
        
        # Validate hour range
        if not (0 <= self.config.disaggregation.hour_range_start < 24):
            self.logger.error("Hour range start must be between 0 and 23")
            is_valid = False
        
        if not (1 <= self.config.disaggregation.hour_range_end <= 48):
            self.logger.error("Hour range end must be between 1 and 48")
            is_valid = False
        
        if self.config.disaggregation.hour_range_start >= self.config.disaggregation.hour_range_end:
            self.logger.error("Hour range start must be less than hour range end")
            is_valid = False
        
        return is_valid
    
    def get_section(self, section_name: str) -> Any:
        """
        Get a specific configuration section.
        
        Args:
            section_name: Name of the configuration section
            
        Returns:
            Configuration section object
        """
        if hasattr(self.config, section_name):
            return getattr(self.config, section_name)
        else:
            raise ValueError(f"Unknown configuration section: {section_name}")
    
    def print_config(self):
        """Print the current configuration in a readable format."""
        config_dict = self._config_to_dict(self.config)
        
        print("Current Configuration:")
        print("=" * 50)
        
        for section_name, section_data in config_dict.items():
            print(f"\n[{section_name.upper()}]")
            for key, value in section_data.items():
                print(f"  {key}: {value}")
        
        print("=" * 50)
